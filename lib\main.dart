import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'services/theme_service.dart';
import 'services/storage_service.dart';
import 'services/analytics_service.dart';
import 'services/error_service.dart';
import 'services/notification_service.dart';
import 'services/security_service.dart';
import 'services/loan_application_service.dart';
import 'services/mock_loan_service.dart';

import 'screens/dashboard_screen.dart';
import 'screens/loan_application_form_screen.dart';
import 'screens/loan_details_screen.dart';
import 'screens/login_screen.dart';
import 'screens/mock_data_demo_screen.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize core services
  await _initializeServices();

  // Set system UI overlay style
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
      systemNavigationBarColor: Colors.white,
      systemNavigationBarIconBrightness: Brightness.dark,
    ),
  );

  // Set preferred orientations
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  runApp(
    const ProviderScope(
      child: LoanCreditApp(),
    ),
  );
}

Future<void> _initializeServices() async {
  try {
    // Initialize storage service first
    final storageService = StorageService();
    await storageService.initialize();

    // Initialize other services

    final loanService = AppConfig.useMockData
        ? MockLoanApplicationService()
        : LoanApplicationService();
    await loanService.initialize();

    final analyticsService = AnalyticsService();
    await analyticsService.initialize();

    final errorService = ErrorService();
    await errorService.initialize();

    final notificationService = NotificationService();
    await notificationService.initialize();

    final securityService = SecurityService();
    await securityService.initialize();

    print('✅ All services initialized successfully');
  } catch (e, stackTrace) {
    print('❌ Failed to initialize services: $e');
    print('Stack trace: $stackTrace');
  }
}

class LoanCreditApp extends ConsumerStatefulWidget {
  const LoanCreditApp({super.key});

  @override
  ConsumerState<LoanCreditApp> createState() => _LoanCreditAppState();
}

class _LoanCreditAppState extends ConsumerState<LoanCreditApp> {
  late ThemeService _themeService;

  @override
  void initState() {
    super.initState();
    _themeService = ThemeService();
    _initializeTheme();
  }

  Future<void> _initializeTheme() async {
    await _themeService.initialize();
    if (mounted) {
      setState(() {}); // Trigger rebuild with loaded theme
    }
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Loan Credit Mobile',
      debugShowCheckedModeBanner: false,

      // Theme configuration
      theme: _themeService.lightTheme,
      darkTheme: _themeService.darkTheme,
      themeMode: _themeService.themeMode,

      // Initial route - start with login
      initialRoute: '/login',

      // Route configuration
      routes: {
        '/login': (context) => const LoginScreen(),
        '/': (context) => const DashboardScreen(),
        '/dashboard': (context) => const DashboardScreen(),
        '/new-application': (context) => const LoanApplicationFormScreen(),
        '/mock-demo': (context) => const MockDataDemoScreen(),
      },

      // Route generator for dynamic routes
      onGenerateRoute: (settings) {
        switch (settings.name) {
          case '/loan-details':
            final args = settings.arguments as Map<String, dynamic>?;
            final applicationId = args?['applicationId'] as String?;
            if (applicationId != null) {
              return _createRoute(
                LoanDetailsScreen(applicationId: applicationId),
              );
            }
            break;
          case '/edit-application':
            final args = settings.arguments as Map<String, dynamic>?;
            final applicationId = args?['applicationId'] as String?;
            if (applicationId != null) {
              return _createRoute(
                LoanApplicationFormScreen(
                  applicationId: applicationId,
                  isEditing: true,
                ),
              );
            }
            break;
        }

        // Return 404 page for unknown routes
        return _createRoute(const NotFoundScreen());
      },

      // Global error handling
      builder: (context, child) {
        ErrorWidget.builder = (FlutterErrorDetails errorDetails) {
          return _buildErrorWidget(errorDetails);
        };

        return child ?? const SizedBox.shrink();
      },

      // Localization
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [
        Locale('en', 'US'),
        Locale('bn', 'BD'), // Bengali for Bangladesh
      ],

      // Accessibility
      showSemanticsDebugger: false,
    );
  }

  Route<T> _createRoute<T extends Object?>(Widget page) {
    return PageRouteBuilder<T>(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        const begin = Offset(1.0, 0.0);
        const end = Offset.zero;
        const curve = Curves.easeInOutCubic;

        var tween = Tween(begin: begin, end: end).chain(
          CurveTween(curve: curve),
        );

        return SlideTransition(
          position: animation.drive(tween),
          child: child,
        );
      },
      transitionDuration: const Duration(milliseconds: 300),
    );
  }

  Widget _buildErrorWidget(FlutterErrorDetails errorDetails) {
    return Material(
      child: Container(
        color: Colors.red[50],
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              color: Colors.red[700],
              size: 48,
            ),
            const SizedBox(height: 16),
            Text(
              'Something went wrong',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.red[700],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Please restart the app or contact support if the problem persists.',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                color: Colors.red[600],
              ),
            ),
            const SizedBox(height: 16),
            if (kDebugMode) ...[
              const Divider(),
              const SizedBox(height: 8),
              Text(
                'Debug Info:',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: Colors.red[800],
                ),
              ),
              const SizedBox(height: 4),
              Expanded(
                child: SingleChildScrollView(
                  child: Text(
                    errorDetails.toString(),
                    style: TextStyle(
                      fontSize: 10,
                      fontFamily: 'monospace',
                      color: Colors.red[600],
                    ),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// 404 Not Found Screen
class NotFoundScreen extends StatelessWidget {
  const NotFoundScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Page Not Found'),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(32),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.search_off,
                size: 120,
                color: Colors.grey[400],
              ),
              const SizedBox(height: 32),
              Text(
                '404',
                style: Theme.of(context).textTheme.displayLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Colors.grey[600],
                    ),
              ),
              const SizedBox(height: 16),
              Text(
                'Page Not Found',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: Colors.grey[700],
                    ),
              ),
              const SizedBox(height: 8),
              Text(
                'The page you are looking for does not exist or has been moved.',
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: Colors.grey[600],
                    ),
              ),
              const SizedBox(height: 48),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: () {
                    Navigator.of(context).pushNamedAndRemoveUntil(
                      '/dashboard',
                      (route) => false,
                    );
                  },
                  icon: const Icon(Icons.home),
                  label: const Text('Go to Dashboard'),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 16),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: const Text('Go Back'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// Debug mode check
const bool kDebugMode = bool.fromEnvironment('dart.vm.product') == false;

/// Global app configuration
class AppConfig {
  static const String appName = 'Loan Credit Mobile';
  static const String version = '1.0.0';
  static const String buildNumber = '1';

  // API Configuration
  static const String baseUrl = 'https://lc-groupscop.up.railway.app/v1/';
  static const String apiVersion = 'v1';

  // Feature flags
  static const bool enableAnalytics = true;
  static const bool enableCrashReporting = true;
  static const bool enableBiometricAuth = true;
  static const bool enableOfflineMode = true;
  static const bool useMockData = true; // Set to false when backend is ready

  // UI Configuration
  static const int maxFileUploadSize = 10 * 1024 * 1024; // 10MB
  static const int sessionTimeoutMinutes = 30;
  static const int maxRetryAttempts = 3;

  // Validation limits
  static const double minLoanAmount = 5000;
  static const double maxLoanAmount = 1000000;
  static const int minLoanTerm = 3;
  static const int maxLoanTerm = 60;

  // Cache configuration
  static const int cacheExpiryHours = 24;
  static const int maxCacheSize = 100 * 1024 * 1024; // 100MB
}

/// Global constants
class AppConstants {
  // Storage keys
  static const String themeKey = 'app_theme';
  static const String languageKey = 'app_language';
  static const String userTokenKey = 'user_token';
  static const String biometricEnabledKey = 'biometric_enabled';
  static const String onboardingCompletedKey = 'onboarding_completed';

  // Notification channels
  static const String defaultChannelId = 'default_notifications';
  static const String loanStatusChannelId = 'loan_status_notifications';
  static const String reminderChannelId = 'reminder_notifications';
  static const String marketingChannelId = 'marketing_notifications';

  // File types
  static const List<String> allowedImageTypes = ['jpg', 'jpeg', 'png', 'pdf'];
  static const List<String> allowedDocumentTypes = ['pdf', 'doc', 'docx'];

  // Regex patterns
  static const String phonePattern = r'^(\+88)?01[3-9]\d{8}$';
  static const String nidPattern = r'^\d{10}$|^\d{13}$|^\d{17}$';
  static const String emailPattern = r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$';

  // Error messages
  static const String networkErrorMessage =
      'Please check your internet connection and try again.';
  static const String serverErrorMessage =
      'Server error occurred. Please try again later.';
  static const String validationErrorMessage =
      'Please check your input and try again.';
  static const String unauthorizedErrorMessage =
      'Session expired. Please login again.';
}

/// App-wide utility functions
class AppUtils {
  /// Format currency amount
  static String formatCurrency(double amount, {String symbol = '৳'}) {
    if (amount >= 100000) {
      return '$symbol${(amount / 100000).toStringAsFixed(1)}L';
    } else if (amount >= 1000) {
      return '$symbol${(amount / 1000).toStringAsFixed(1)}K';
    }
    return '$symbol${amount.toStringAsFixed(0)}';
  }

  /// Format phone number
  static String formatPhoneNumber(String phone) {
    if (phone.startsWith('+88')) {
      phone = phone.substring(3);
    }
    if (phone.length == 11) {
      return '${phone.substring(0, 4)} ${phone.substring(4, 7)} ${phone.substring(7)}';
    }
    return phone;
  }

  /// Validate NID number
  static bool isValidNID(String nid) {
    return RegExp(AppConstants.nidPattern).hasMatch(nid);
  }

  /// Validate phone number
  static bool isValidPhone(String phone) {
    return RegExp(AppConstants.phonePattern).hasMatch(phone);
  }

  /// Validate email
  static bool isValidEmail(String email) {
    return RegExp(AppConstants.emailPattern).hasMatch(email);
  }

  /// Get file extension
  static String getFileExtension(String fileName) {
    return fileName.split('.').last.toLowerCase();
  }

  /// Check if file type is allowed
  static bool isAllowedFileType(String fileName, List<String> allowedTypes) {
    final extension = getFileExtension(fileName);
    return allowedTypes.contains(extension);
  }

  /// Generate unique ID
  static String generateUniqueId() {
    return DateTime.now().millisecondsSinceEpoch.toString();
  }

  /// Calculate age from date of birth
  static int calculateAge(DateTime dateOfBirth) {
    final now = DateTime.now();
    int age = now.year - dateOfBirth.year;
    if (now.month < dateOfBirth.month ||
        (now.month == dateOfBirth.month && now.day < dateOfBirth.day)) {
      age--;
    }
    return age;
  }

  /// Format date for display
  static String formatDate(DateTime date, {String format = 'dd/MM/yyyy'}) {
    switch (format) {
      case 'dd/MM/yyyy':
        return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
      case 'MMM dd, yyyy':
        const months = [
          'Jan',
          'Feb',
          'Mar',
          'Apr',
          'May',
          'Jun',
          'Jul',
          'Aug',
          'Sep',
          'Oct',
          'Nov',
          'Dec'
        ];
        return '${months[date.month - 1]} ${date.day}, ${date.year}';
      default:
        return date.toString();
    }
  }

  /// Show snackbar message
  static void showSnackBar(BuildContext context, String message,
      {bool isError = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : null,
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  /// Show loading dialog
  static void showLoadingDialog(BuildContext context,
      {String message = 'Loading...'}) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        content: Row(
          children: [
            const CircularProgressIndicator(),
            const SizedBox(width: 16),
            Text(message),
          ],
        ),
      ),
    );
  }

  /// Hide loading dialog
  static void hideLoadingDialog(BuildContext context) {
    Navigator.of(context).pop();
  }
}
