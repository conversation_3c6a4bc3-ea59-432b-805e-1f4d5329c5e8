import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../utils/colors.dart';
import '../widgets/ui_components.dart';
import '../providers/loan_application_provider.dart';
import '../models/loan_application.dart';
import '../main.dart';
import 'dart:math';

/// Modern mock data demo screen for testing and development
class MockDataDemoScreen extends ConsumerStatefulWidget {
  const MockDataDemoScreen({super.key});

  @override
  ConsumerState<MockDataDemoScreen> createState() => _MockDataDemoScreenState();
}

class _MockDataDemoScreenState extends ConsumerState<MockDataDemoScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  
  bool _isGenerating = false;
  int _generatedCount = 0;
  final Random _random = Random();

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));
    
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final applicationsState = ref.watch(loanApplicationsProvider);
    
    return Scaffold(
      backgroundColor: AppColors.background,
      body: CustomScrollView(
        slivers: [
          _buildAppBar(),
          SliverPadding(
            padding: const EdgeInsets.all(16.0),
            sliver: SliverList(
              delegate: SliverChildListDelegate([
                _buildHeaderCard(),
                const SizedBox(height: 20),
                _buildStatsSection(applicationsState),
                const SizedBox(height: 20),
                _buildActionsSection(),
                const SizedBox(height: 20),
                _buildDataPreview(applicationsState),
                const SizedBox(height: 20),
                _buildDeveloperTools(),
              ]),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAppBar() {
    return SliverAppBar(
      expandedHeight: 120,
      floating: false,
      pinned: true,
      backgroundColor: AppColors.primary,
      flexibleSpace: FlexibleSpaceBar(
        title: const Text(
          'Mock Data Demo',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        background: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                AppColors.primary,
                AppColors.secondary,
              ],
            ),
          ),
        ),
      ),
      leading: IconButton(
        icon: const Icon(Icons.arrow_back, color: Colors.white),
        onPressed: () => Navigator.of(context).pop(),
      ),
      actions: [
        _InfoButton(),
        const SizedBox(width: 8),
      ],
    );
  }

  Widget _buildHeaderCard() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: _HeaderCard(),
      ),
    );
  }

  Widget _buildStatsSection(LoanApplicationsState state) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: Tween<Offset>(
          begin: const Offset(0, 0.4),
          end: Offset.zero,
        ).animate(CurvedAnimation(
          parent: _animationController,
          curve: const Interval(0.2, 1.0, curve: Curves.easeOutCubic),
        )),
        child: _StatsSection(state: state),
      ),
    );
  }

  Widget _buildActionsSection() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: Tween<Offset>(
          begin: const Offset(0, 0.5),
          end: Offset.zero,
        ).animate(CurvedAnimation(
          parent: _animationController,
          curve: const Interval(0.3, 1.0, curve: Curves.easeOutCubic),
        )),
        child: _ActionsSection(
          isGenerating: _isGenerating,
          onGenerateData: _generateMockData,
          onClearData: _clearAllData,
        ),
      ),
    );
  }

  Widget _buildDataPreview(LoanApplicationsState state) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: Tween<Offset>(
          begin: const Offset(0, 0.6),
          end: Offset.zero,
        ).animate(CurvedAnimation(
          parent: _animationController,
          curve: const Interval(0.4, 1.0, curve: Curves.easeOutCubic),
        )),
        child: _DataPreviewSection(state: state),
      ),
    );
  }

  Widget _buildDeveloperTools() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: Tween<Offset>(
          begin: const Offset(0, 0.7),
          end: Offset.zero,
        ).animate(CurvedAnimation(
          parent: _animationController,
          curve: const Interval(0.5, 1.0, curve: Curves.easeOutCubic),
        )),
        child: _DeveloperToolsSection(),
      ),
    );
  }

  Future<void> _generateMockData() async {
    setState(() {
      _isGenerating = true;
      _generatedCount = 0;
    });

    try {
      final applications = _createMockApplications(10);
      
      for (int i = 0; i < applications.length; i++) {
        await ref.read(loanApplicationsProvider.notifier).addApplication(applications[i]);
        setState(() {
          _generatedCount = i + 1;
        });
        await Future.delayed(const Duration(milliseconds: 200));
      }
      
      if (mounted) {
        AppUtils.showSnackBar(
          context,
          'Successfully generated ${applications.length} mock applications!',
        );
      }
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(
          context,
          'Error generating mock data: $e',
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isGenerating = false;
          _generatedCount = 0;
        });
      }
    }
  }

  Future<void> _clearAllData() async {
    final confirmed = await _showClearConfirmation();
    if (!confirmed) return;

    try {
      await ref.read(loanApplicationsProvider.notifier).clearAllApplications();
      if (mounted) {
        AppUtils.showSnackBar(
          context,
          'All mock data cleared successfully!',
        );
      }
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(
          context,
          'Error clearing data: $e',
        );
      }
    }
  }

  Future<bool> _showClearConfirmation() async {
    return await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear All Data'),
        content: const Text(
          'Are you sure you want to clear all mock data? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Clear All'),
          ),
        ],
      ),
    ) ?? false;
  }

  List<LoanApplication> _createMockApplications(int count) {
    final applications = <LoanApplication>[];
    final names = [
      'John Doe', 'Jane Smith', 'Michael Johnson', 'Emily Davis',
      'David Wilson', 'Sarah Brown', 'Robert Taylor', 'Lisa Anderson',
      'James Martinez', 'Jennifer Garcia'
    ];
    
    final purposes = [
      'Home renovation', 'Business expansion', 'Education expenses',
      'Medical emergency', 'Debt consolidation', 'Vehicle purchase',
      'Wedding expenses', 'Travel', 'Investment opportunity', 'Emergency fund'
    ];
    
    final employmentStatuses = [
      'Full-time employed', 'Part-time employed', 'Self-employed',
      'Freelancer', 'Business owner'
    ];
    
    final addresses = [
      '123 Main St, New York, NY 10001',
      '456 Oak Ave, Los Angeles, CA 90210',
      '789 Pine Rd, Chicago, IL 60601',
      '321 Elm St, Houston, TX 77001',
      '654 Maple Dr, Phoenix, AZ 85001'
    ];

    for (int i = 0; i < count; i++) {
      final name = names[_random.nextInt(names.length)];
      final email = '${name.toLowerCase().replaceAll(' ', '.')}@example.com';
      final phone = '+1${_random.nextInt(900) + 100}${_random.nextInt(900) + 100}${_random.nextInt(9000) + 1000}';
      
      applications.add(LoanApplication(
        id: 'mock_${DateTime.now().millisecondsSinceEpoch}_$i',
        applicantName: name,
        email: email,
        phoneNumber: phone,
        address: addresses[_random.nextInt(addresses.length)],
        requestedAmount: (_random.nextInt(50) + 5) * 1000.0, // 5k to 55k
        loanType: LoanType.values[_random.nextInt(LoanType.values.length)],
        purpose: purposes[_random.nextInt(purposes.length)],
        monthlyIncome: (_random.nextInt(8) + 3) * 1000.0, // 3k to 11k
        employmentStatus: employmentStatuses[_random.nextInt(employmentStatuses.length)],
        creditScore: _random.nextInt(300) + 500, // 500 to 800
        status: LoanStatus.values[_random.nextInt(LoanStatus.values.length)],
        applicationDate: DateTime.now().subtract(
          Duration(days: _random.nextInt(30)),
        ),
        documents: _generateMockDocuments(),
        interestRate: (_random.nextDouble() * 10 + 5), // 5% to 15%
        loanTerm: (_random.nextInt(5) + 1) * 12, // 12 to 60 months
      ));
    }
    
    return applications;
  }

  List<String> _generateMockDocuments() {
    final allDocuments = [
      'Identity Proof (Passport)',
      'Address Proof (Utility Bill)',
      'Income Certificate',
      'Bank Statements (3 months)',
      'Employment Letter',
      'Tax Returns',
      'Credit Report',
      'Property Documents',
    ];
    
    final count = _random.nextInt(5) + 2; // 2 to 6 documents
    final selectedDocs = <String>[];
    
    while (selectedDocs.length < count && selectedDocs.length < allDocuments.length) {
      final doc = allDocuments[_random.nextInt(allDocuments.length)];
      if (!selectedDocs.contains(doc)) {
        selectedDocs.add(doc);
      }
    }
    
    return selectedDocs;
  }
}

/// Header card component
class _HeaderCard extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.primary,
            AppColors.secondary,
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withOpacity(0.3),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: const Icon(
                  Icons.science_outlined,
                  color: Colors.white,
                  size: 30,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Development Tools',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Generate and manage mock data for testing',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.white.withOpacity(0.9),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.white.withOpacity(0.2),
              ),
            ),
            child: Row(
              children: [
                const Icon(
                  Icons.info_outline,
                  color: Colors.white,
                  size: 20,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'This screen is for development and testing purposes only.',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.white.withOpacity(0.9),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// Stats section component
class _StatsSection extends StatelessWidget {
  final LoanApplicationsState state;

  const _StatsSection({required this.state});

  @override
  Widget build(BuildContext context) {
    final totalApplications = state.applications.length;
    final pendingCount = state.applications.where((app) => app.status == LoanStatus.pending).length;
    final approvedCount = state.applications.where((app) => app.status == LoanStatus.approved).length;
    final rejectedCount = state.applications.where((app) => app.status == LoanStatus.rejected).length;
    
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.analytics_outlined,
                color: AppColors.primary,
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'Data Statistics',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: AppColors.textPrimary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                child: _StatCard(
                  title: 'Total',
                  value: totalApplications.toString(),
                  color: AppColors.primary,
                  icon: Icons.apps,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _StatCard(
                  title: 'Pending',
                  value: pendingCount.toString(),
                  color: AppColors.pending,
                  icon: Icons.pending,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _StatCard(
                  title: 'Approved',
                  value: approvedCount.toString(),
                  color: AppColors.approved,
                  icon: Icons.check_circle,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _StatCard(
                  title: 'Rejected',
                  value: rejectedCount.toString(),
                  color: AppColors.rejected,
                  icon: Icons.cancel,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

/// Stat card component
class _StatCard extends StatelessWidget {
  final String title;
  final String value;
  final Color color;
  final IconData icon;

  const _StatCard({
    required this.title,
    required this.value,
    required this.color,
    required this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withOpacity(0.3),
        ),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: color,
            size: 24,
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: color.withOpacity(0.8),
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}

/// Actions section component
class _ActionsSection extends StatelessWidget {
  final bool isGenerating;
  final VoidCallback onGenerateData;
  final VoidCallback onClearData;

  const _ActionsSection({
    required this.isGenerating,
    required this.onGenerateData,
    required this.onClearData,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.build_outlined,
                color: AppColors.primary,
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'Quick Actions',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: AppColors.textPrimary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          _ActionButton(
            icon: Icons.add_circle_outline,
            title: 'Generate Mock Data',
            subtitle: 'Create 10 sample loan applications',
            color: AppColors.secondary,
            isLoading: isGenerating,
            onTap: isGenerating ? null : onGenerateData,
          ),
          const SizedBox(width: 12),
          _ActionButton(
            icon: Icons.delete_outline,
            title: 'Clear All Data',
            subtitle: 'Remove all existing applications',
            color: Colors.red,
            onTap: isGenerating ? null : onClearData,
          ),
        ],
      ),
    );
  }
}

/// Action button component
class _ActionButton extends StatelessWidget {
  final IconData icon;
  final String title;
  final String subtitle;
  final Color color;
  final bool isLoading;
  final VoidCallback? onTap;

  const _ActionButton({
    required this.icon,
    required this.title,
    required this.subtitle,
    required this.color,
    this.isLoading = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: onTap == null ? Colors.grey.shade300 : color.withOpacity(0.3),
              ),
              color: onTap == null ? Colors.grey.shade50 : color.withOpacity(0.05),
            ),
            child: Row(
              children: [
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: onTap == null ? Colors.grey.shade300 : color.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: isLoading
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: AppColors.primary,
                          ),
                        )
                      : Icon(
                          icon,
                          color: onTap == null ? Colors.grey.shade500 : color,
                          size: 24,
                        ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: onTap == null ? Colors.grey.shade500 : AppColors.textPrimary,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        subtitle,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: onTap == null ? Colors.grey.shade400 : Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
                if (onTap != null && !isLoading)
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 16,
                    color: Colors.grey.shade400,
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// Data preview section component
class _DataPreviewSection extends StatelessWidget {
  final LoanApplicationsState state;

  const _DataPreviewSection({required this.state});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.preview_outlined,
                color: AppColors.primary,
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'Data Preview',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: AppColors.textPrimary,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              if (state.applications.isNotEmpty)
                Text(
                  '${state.applications.length} items',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey.shade600,
                  ),
                ),
            ],
          ),
          const SizedBox(height: 16),
          if (state.applications.isEmpty)
            _EmptyDataView()
          else
            Column(
              children: state.applications.take(3).map((application) {
                return _DataPreviewItem(application: application);
              }).toList(),
            ),
          if (state.applications.length > 3)
            Padding(
              padding: const EdgeInsets.only(top: 12),
              child: Center(
                child: Text(
                  'and ${state.applications.length - 3} more...',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey.shade600,
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}

/// Empty data view component
class _EmptyDataView extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(32),
      child: Column(
        children: [
          Icon(
            Icons.inbox_outlined,
            size: 48,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 12),
          Text(
            'No data available',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey.shade600,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'Generate mock data to see preview',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.grey.shade500,
            ),
          ),
        ],
      ),
    );
  }
}

/// Data preview item component
class _DataPreviewItem extends StatelessWidget {
  final LoanApplication application;

  const _DataPreviewItem({required this.application});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.grey.shade200,
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: _getStatusColor(application.status).withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              _getStatusIcon(application.status),
              color: _getStatusColor(application.status),
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  application.applicantName,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  '${AppUtils.formatCurrency(application.requestedAmount)} • ${_getStatusText(application.status)}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
          Text(
            '#${application.id.substring(0, 6)}',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.grey.shade500,
              fontFamily: 'monospace',
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(LoanStatus status) {
    switch (status) {
      case LoanStatus.pending:
        return AppColors.pending;
      case LoanStatus.approved:
        return AppColors.approved;
      case LoanStatus.rejected:
        return AppColors.rejected;
      case LoanStatus.disbursed:
        return AppColors.secondary;
    }
  }

  IconData _getStatusIcon(LoanStatus status) {
    switch (status) {
      case LoanStatus.pending:
        return Icons.pending;
      case LoanStatus.approved:
        return Icons.check_circle;
      case LoanStatus.rejected:
        return Icons.cancel;
      case LoanStatus.disbursed:
        return Icons.account_balance_wallet;
    }
  }

  String _getStatusText(LoanStatus status) {
    switch (status) {
      case LoanStatus.pending:
        return 'Pending';
      case LoanStatus.approved:
        return 'Approved';
      case LoanStatus.rejected:
        return 'Rejected';
      case LoanStatus.disbursed:
        return 'Disbursed';
    }
  }
}

/// Developer tools section component
class _DeveloperToolsSection extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.code_outlined,
                color: AppColors.primary,
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'Developer Tools',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: AppColors.textPrimary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _DeveloperToolItem(
            icon: Icons.bug_report_outlined,
            title: 'Debug Mode',
            subtitle: 'Enable detailed logging',
            onTap: () {
              AppUtils.showSnackBar(context, 'Debug mode toggled!');
            },
          ),
          _DeveloperToolItem(
            icon: Icons.refresh_outlined,
            title: 'Reset App State',
            subtitle: 'Clear all cached data',
            onTap: () {
              AppUtils.showSnackBar(context, 'App state reset!');
            },
          ),
          _DeveloperToolItem(
            icon: Icons.download_outlined,
            title: 'Export Data',
            subtitle: 'Download data as JSON',
            onTap: () {
              AppUtils.showSnackBar(context, 'Export feature coming soon!');
            },
          ),
        ],
      ),
    );
  }
}

/// Developer tool item component
class _DeveloperToolItem extends StatelessWidget {
  final IconData icon;
  final String title;
  final String subtitle;
  final VoidCallback onTap;

  const _DeveloperToolItem({
    required this.icon,
    required this.title,
    required this.subtitle,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(12),
        margin: const EdgeInsets.only(bottom: 8),
        child: Row(
          children: [
            Icon(
              icon,
              color: AppColors.primary,
              size: 20,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    subtitle,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),
            const Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: Colors.grey,
            ),
          ],
        ),
      ),
    );
  }
}

/// Info button component
class _InfoButton extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return IconButton(
      icon: const Icon(Icons.info_outline, color: Colors.white),
      onPressed: () => _showInfoDialog(context),
    );
  }

  void _showInfoDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Mock Data Demo'),
        content: const Text(
          'This screen allows developers to generate and manage mock data for testing purposes. '
          'Use the "Generate Mock Data" button to create sample loan applications, '
          'and "Clear All Data" to remove all existing data.\n\n'
          'This feature is intended for development and testing only.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Got it'),
          ),
        ],
      ),
    );
  }
}