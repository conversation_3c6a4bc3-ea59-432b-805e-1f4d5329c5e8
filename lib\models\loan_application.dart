/// Enum for loan application status
enum LoanStatus {
  pending('Pending'),
  approved('Approved'),
  rejected('Rejected'),
  disbursed('Disbursed');

  const LoanStatus(this.displayName);
  final String displayName;

  /// Get status color for UI
  static String getStatusColor(LoanStatus status) {
    switch (status) {
      case LoanStatus.pending:
        return 'orange';
      case LoanStatus.approved:
        return 'green';
      case LoanStatus.rejected:
        return 'red';
      case LoanStatus.disbursed:
        return 'blue';
    }
  }
}

/// Main loan application model with enhanced validation and serialization
class LoanApplication {
  final String id;
  final LoanStatus status;
  final DateTime applicationDate;
  final double requestedAmount;
  final double serviceFee;
  final int loanTerm;
  final String? decisionReason;
  final String? groupName;
  final PortfolioOfficer portfolioOfficer;
  final CpoApprover? cpoApprover;
  final Applicant applicant;
  final Business business;
  final Family family;
  final Assets assets;
  final String agreementUrl;
  final Guarantor guarantor;
  final List<GroupMember> groupMembers;
  final Map<String, String> documents;
  final DateTime createdAt;
  final DateTime? updatedAt;

  LoanApplication({
    required this.id,
    required this.status,
    required this.applicationDate,
    required this.requestedAmount,
    required this.serviceFee,
    required this.loanTerm,
    this.decisionReason,
    this.groupName,
    required this.portfolioOfficer,
    this.cpoApprover,
    required this.applicant,
    required this.business,
    required this.family,
    required this.assets,
    required this.agreementUrl,
    required this.guarantor,
    required this.groupMembers,
    required this.documents,
    required this.createdAt,
    this.updatedAt,
  }) : assert(requestedAmount > 0, 'Requested amount must be positive'),
       assert(serviceFee >= 0, 'Service fee cannot be negative'),
       assert(loanTerm > 0, 'Loan term must be positive');

  /// Calculate total amount including service fee
  double get totalAmount => requestedAmount + serviceFee;

  /// Check if application is editable
  bool get isEditable => status == LoanStatus.pending;

  /// Check if application can be approved
  bool get canBeApproved => status == LoanStatus.pending;

  /// Check if application can be disbursed
  bool get canBeDisbursed => status == LoanStatus.approved;

  /// Create a copy with updated fields
  LoanApplication copyWith({
    String? id,
    LoanStatus? status,
    DateTime? applicationDate,
    double? requestedAmount,
    double? serviceFee,
    int? loanTerm,
    String? decisionReason,
    String? groupName,
    PortfolioOfficer? portfolioOfficer,
    CpoApprover? cpoApprover,
    Applicant? applicant,
    Business? business,
    Family? family,
    Assets? assets,
    String? agreementUrl,
    Guarantor? guarantor,
    List<GroupMember>? groupMembers,
    Map<String, String>? documents,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return LoanApplication(
      id: id ?? this.id,
      status: status ?? this.status,
      applicationDate: applicationDate ?? this.applicationDate,
      requestedAmount: requestedAmount ?? this.requestedAmount,
      serviceFee: serviceFee ?? this.serviceFee,
      loanTerm: loanTerm ?? this.loanTerm,
      decisionReason: decisionReason ?? this.decisionReason,
      groupName: groupName ?? this.groupName,
      portfolioOfficer: portfolioOfficer ?? this.portfolioOfficer,
      cpoApprover: cpoApprover ?? this.cpoApprover,
      applicant: applicant ?? this.applicant,
      business: business ?? this.business,
      family: family ?? this.family,
      assets: assets ?? this.assets,
      agreementUrl: agreementUrl ?? this.agreementUrl,
      guarantor: guarantor ?? this.guarantor,
      groupMembers: groupMembers ?? this.groupMembers,
      documents: documents ?? this.documents,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }

  /// Convert to JSON for API calls
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'status': status.name,
      'applicationDate': applicationDate.toIso8601String(),
      'requestedAmount': requestedAmount,
      'serviceFee': serviceFee,
      'loanTerm': loanTerm,
      'decisionReason': decisionReason,
      'groupName': groupName,
      'portfolioOfficer': portfolioOfficer.toJson(),
      'cpoApprover': cpoApprover?.toJson(),
      'applicant': applicant.toJson(),
      'business': business.toJson(),
      'family': family.toJson(),
      'assets': assets.toJson(),
      'agreementUrl': agreementUrl,
      'guarantor': guarantor.toJson(),
      'groupMembers': groupMembers.map((member) => member.toJson()).toList(),
      'documents': documents,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  /// Create from JSON
  factory LoanApplication.fromJson(Map<String, dynamic> json) {
    return LoanApplication(
      id: json['id'],
      status: LoanStatus.values.firstWhere((e) => e.name == json['status']),
      applicationDate: DateTime.parse(json['applicationDate']),
      requestedAmount: json['requestedAmount'].toDouble(),
      serviceFee: json['serviceFee'].toDouble(),
      loanTerm: json['loanTerm'],
      decisionReason: json['decisionReason'],
      groupName: json['groupName'],
      portfolioOfficer: PortfolioOfficer.fromJson(json['portfolioOfficer']),
      cpoApprover: json['cpoApprover'] != null 
          ? CpoApprover.fromJson(json['cpoApprover']) 
          : null,
      applicant: Applicant.fromJson(json['applicant']),
      business: Business.fromJson(json['business']),
      family: Family.fromJson(json['family']),
      assets: Assets.fromJson(json['assets']),
      agreementUrl: json['agreementUrl'],
      guarantor: Guarantor.fromJson(json['guarantor']),
      groupMembers: (json['groupMembers'] as List)
          .map((member) => GroupMember.fromJson(member))
          .toList(),
      documents: Map<String, String>.from(json['documents']),
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: json['updatedAt'] != null 
          ? DateTime.parse(json['updatedAt']) 
          : null,
    );
  }
}

/// Portfolio Officer model with enhanced functionality
class PortfolioOfficer {
  final String id;
  final String name;
  final String email;
  final String phone;
  final String? profileImageUrl;
  
  PortfolioOfficer({
    required this.id,
    required this.name,
    required this.email,
    required this.phone,
    this.profileImageUrl,
  }) : assert(name.isNotEmpty, 'Name cannot be empty'),
       assert(email.contains('@'), 'Invalid email format'),
       assert(phone.isNotEmpty, 'Phone cannot be empty');

  /// Get initials for avatar display
  String get initials {
    final names = name.split(' ');
    if (names.length >= 2) {
      return '${names[0][0]}${names[1][0]}'.toUpperCase();
    }
    return name.isNotEmpty ? name[0].toUpperCase() : 'PO';
  }

  Map<String, dynamic> toJson() => {
    'id': id,
    'name': name,
    'email': email,
    'phone': phone,
    'profileImageUrl': profileImageUrl,
  };

  factory PortfolioOfficer.fromJson(Map<String, dynamic> json) => PortfolioOfficer(
    id: json['id'],
    name: json['name'],
    email: json['email'],
    phone: json['phone'],
    profileImageUrl: json['profileImageUrl'],
  );
}

/// CPO Approver model
class CpoApprover {
  final String id;
  final String name;
  final String email;
  final DateTime? approvalDate;
  final String? approvalComments;
  
  CpoApprover({
    required this.id,
    required this.name,
    required this.email,
    this.approvalDate,
    this.approvalComments,
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    'name': name,
    'email': email,
    'approvalDate': approvalDate?.toIso8601String(),
    'approvalComments': approvalComments,
  };

  factory CpoApprover.fromJson(Map<String, dynamic> json) => CpoApprover(
    id: json['id'],
    name: json['name'],
    email: json['email'],
    approvalDate: json['approvalDate'] != null 
        ? DateTime.parse(json['approvalDate']) 
        : null,
    approvalComments: json['approvalComments'],
  );
}

/// Applicant model with validation and utilities
class Applicant {
  final String name;
  final String address;
  final String nationalId;
  final DateTime dateOfBirth;
  final String gender;
  final String maritalStatus;
  final String education;
  final String occupation;
  final String phoneNumber;
  final String nid;
  final String email;
  final String profileImageUrl;
  final double monthlyIncome;

  Applicant({
    required this.name,
    required this.address,
    required this.nationalId,
    required this.dateOfBirth,
    required this.gender,
    required this.maritalStatus,
    required this.education,
    required this.occupation,
    required this.phoneNumber,
    required this.nid,
    required this.email,
    required this.profileImageUrl,
    required this.monthlyIncome,
  });

  /// Calculate age from date of birth
  int get age {
    final now = DateTime.now();
    int age = now.year - dateOfBirth.year;
    if (now.month < dateOfBirth.month || (now.month == dateOfBirth.month && now.day < dateOfBirth.day)) {
      age--;
    }
    return age;
  }

  /// Check if applicant is eligible (18+ years)
  bool get isEligible => age >= 18;

  Applicant copyWith({
    String? name,
    String? address,
    String? nationalId,
    DateTime? dateOfBirth,
    String? gender,
    String? maritalStatus,
    String? education,
    String? occupation,
    String? phoneNumber,
    String? nid,
    String? email,
    String? profileImageUrl,
    double? monthlyIncome,
  }) {
    return Applicant(
      name: name ?? this.name,
      address: address ?? this.address,
      nationalId: nationalId ?? this.nationalId,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      gender: gender ?? this.gender,
      maritalStatus: maritalStatus ?? this.maritalStatus,
      education: education ?? this.education,
      occupation: occupation ?? this.occupation,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      nid: nid ?? this.nid,
      email: email ?? this.email,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      monthlyIncome: monthlyIncome ?? this.monthlyIncome,
    );
  }

  Map<String, dynamic> toJson() => {
    'name': name,
    'address': address,
    'nationalId': nationalId,
    'dateOfBirth': dateOfBirth.toIso8601String(),
    'gender': gender,
    'maritalStatus': maritalStatus,
    'education': education,
    'occupation': occupation,
    'phoneNumber': phoneNumber,
    'nid': nid,
    'email': email,
    'profileImageUrl': profileImageUrl,
    'monthlyIncome': monthlyIncome,
  };

  factory Applicant.fromJson(Map<String, dynamic> json) {
    return Applicant(
      name: json['name'],
      address: json['address'],
      nationalId: json['nationalId'],
      dateOfBirth: DateTime.parse(json['dateOfBirth']),
      gender: json['gender'],
      maritalStatus: json['maritalStatus'],
      education: json['education'],
      occupation: json['occupation'],
      phoneNumber: json['phoneNumber'],
      nid: json['nid'],
      email: json['email'],
      profileImageUrl: json['profileImageUrl'],
      monthlyIncome: json['monthlyIncome'].toDouble(),
    );
  }
}

/// Business information model
class Business {
  final String name;
  final String type;
  final int yearsInOperation;
  final double monthlyIncome;
  final String address;
  final String location;
  final String description;

  Business({
    required this.name,
    required this.type,
    required this.yearsInOperation,
    required this.monthlyIncome,
    required this.address,
    required this.location,
    required this.description,
  });

  /// Check if business is established (2+ years)
  bool get isEstablished => yearsInOperation >= 2;

  Business copyWith({
    String? name,
    String? type,
    int? yearsInOperation,
    double? monthlyIncome,
    String? address,
    String? location,
    String? description,
  }) {
    return Business(
      name: name ?? this.name,
      type: type ?? this.type,
      yearsInOperation: yearsInOperation ?? this.yearsInOperation,
      monthlyIncome: monthlyIncome ?? this.monthlyIncome,
      address: address ?? this.address,
      location: location ?? this.location,
      description: description ?? this.description,
    );
  }

  Map<String, dynamic> toJson() => {
    'name': name,
    'type': type,
    'yearsInOperation': yearsInOperation,
    'monthlyIncome': monthlyIncome,
    'address': address,
    'location': location,
    'description': description,
  };

  factory Business.fromJson(Map<String, dynamic> json) {
    return Business(
      name: json['name'],
      type: json['type'],
      yearsInOperation: json['yearsInOperation'],
      monthlyIncome: json['monthlyIncome'].toDouble(),
      address: json['address'],
      location: json['location'],
      description: json['description'],
    );
  }
}

/// Family information model
class Family {
  final String? spouseName;
  final String? spousePhone;
  final String maritalStatus;
  final int numberOfChildren;
  final int dependents;
  final double familyIncome;
  final List<String> photoUrls;
  
  Family({
    this.spouseName,
    this.spousePhone,
    required this.maritalStatus,
    required this.numberOfChildren,
    required this.dependents,
    required this.familyIncome,
    required this.photoUrls,
  }) : assert(numberOfChildren >= 0, 'Number of children cannot be negative'),
       assert(dependents >= 0, 'Dependents count cannot be negative'),
       assert(familyIncome >= 0, 'Family income cannot be negative');

  /// Check if applicant has family responsibilities
  bool get hasFamilyResponsibilities => numberOfChildren > 0 || dependents > 0;

  Family copyWith({
    String? spouseName,
    String? spousePhone,
    String? maritalStatus,
    int? numberOfChildren,
    int? dependents,
    double? familyIncome,
    List<String>? photoUrls,
  }) {
    return Family(
      spouseName: spouseName ?? this.spouseName,
      spousePhone: spousePhone ?? this.spousePhone,
      maritalStatus: maritalStatus ?? this.maritalStatus,
      numberOfChildren: numberOfChildren ?? this.numberOfChildren,
      dependents: dependents ?? this.dependents,
      familyIncome: familyIncome ?? this.familyIncome,
      photoUrls: photoUrls ?? this.photoUrls,
    );
  }

  Map<String, dynamic> toJson() => {
    'spouseName': spouseName,
    'spousePhone': spousePhone,
    'maritalStatus': maritalStatus,
    'numberOfChildren': numberOfChildren,
    'dependents': dependents,
    'familyIncome': familyIncome,
    'photoUrls': photoUrls,
  };

  factory Family.fromJson(Map<String, dynamic> json) => Family(
    spouseName: json['spouseName'],
    spousePhone: json['spousePhone'],
    maritalStatus: json['maritalStatus'],
    numberOfChildren: json['numberOfChildren'],
    dependents: json['dependents'],
    familyIncome: json['familyIncome'].toDouble(),
    photoUrls: List<String>.from(json['photoUrls']),
  );
}

/// Assets information model
class Assets {
  final String housePhotoUrl;
  final String otherAssetsPhotoUrl;
  final double estimatedValue;
  final String ownershipType; // 'owned', 'rented', 'family'
  final List<AssetItem> additionalAssets;
  
  Assets({
    required this.housePhotoUrl,
    required this.otherAssetsPhotoUrl,
    required this.estimatedValue,
    required this.ownershipType,
    required this.additionalAssets,
  }) : assert(estimatedValue >= 0, 'Estimated value cannot be negative');

  /// Calculate total asset value
  double get totalValue {
    double total = estimatedValue;
    for (final asset in additionalAssets) {
      total += asset.value;
    }
    return total;
  }

  Map<String, dynamic> toJson() => {
    'housePhotoUrl': housePhotoUrl,
    'otherAssetsPhotoUrl': otherAssetsPhotoUrl,
    'estimatedValue': estimatedValue,
    'ownershipType': ownershipType,
    'additionalAssets': additionalAssets.map((asset) => asset.toJson()).toList(),
  };

  factory Assets.fromJson(Map<String, dynamic> json) => Assets(
    housePhotoUrl: json['housePhotoUrl'],
    otherAssetsPhotoUrl: json['otherAssetsPhotoUrl'],
    estimatedValue: json['estimatedValue'].toDouble(),
    ownershipType: json['ownershipType'],
    additionalAssets: (json['additionalAssets'] as List)
        .map((asset) => AssetItem.fromJson(asset))
        .toList(),
  );
}

/// Individual asset item
class AssetItem {
  final String name;
  final String type;
  final double value;
  final String? photoUrl;
  
  AssetItem({
    required this.name,
    required this.type,
    required this.value,
    this.photoUrl,
  });

  Map<String, dynamic> toJson() => {
    'name': name,
    'type': type,
    'value': value,
    'photoUrl': photoUrl,
  };

  factory AssetItem.fromJson(Map<String, dynamic> json) => AssetItem(
    name: json['name'],
    type: json['type'],
    value: json['value'].toDouble(),
    photoUrl: json['photoUrl'],
  );
}

/// Guarantor model with enhanced validation
class Guarantor {
  final String id;
  final String name;
  final String nid;
  final String phoneNumber;
  final String address;
  final String relationship;
  final String occupation;
  final double monthlyIncome;
  final String collateralPhotoUrl;
  final double collateralValue;

  Guarantor({
    required this.id,
    required this.name,
    required this.nid,
    required this.phoneNumber,
    required this.address,
    required this.relationship,
    required this.occupation,
    required this.monthlyIncome,
    required this.collateralPhotoUrl,
    required this.collateralValue,
  })  : assert(name.isNotEmpty, 'Guarantor name cannot be empty'),
        assert(nid.length >= 10, 'Invalid guarantor NID number'),
        assert(collateralValue > 0, 'Collateral value must be positive');

  Guarantor copyWith({
    String? id,
    String? name,
    String? nid,
    String? phoneNumber,
    String? address,
    String? relationship,
    String? occupation,
    double? monthlyIncome,
    String? collateralPhotoUrl,
    double? collateralValue,
  }) {
    return Guarantor(
      id: id ?? this.id,
      name: name ?? this.name,
      nid: nid ?? this.nid,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      address: address ?? this.address,
      relationship: relationship ?? this.relationship,
      occupation: occupation ?? this.occupation,
      monthlyIncome: monthlyIncome ?? this.monthlyIncome,
      collateralPhotoUrl: collateralPhotoUrl ?? this.collateralPhotoUrl,
      collateralValue: collateralValue ?? this.collateralValue,
    );
  }

  Map<String, dynamic> toJson() => {
        'id': id,
        'name': name,
        'nid': nid,
        'phoneNumber': phoneNumber,
        'address': address,
        'relationship': relationship,
        'occupation': occupation,
        'monthlyIncome': monthlyIncome,
        'collateralPhotoUrl': collateralPhotoUrl,
        'collateralValue': collateralValue,
      };

  factory Guarantor.fromJson(Map<String, dynamic> json) => Guarantor(
        id: json['id'],
        name: json['name'],
        nid: json['nid'],
        phoneNumber: json['phoneNumber'],
        address: json['address'],
        relationship: json['relationship'],
        occupation: json['occupation'],
        monthlyIncome: json['monthlyIncome'].toDouble(),
        collateralPhotoUrl: json['collateralPhotoUrl'],
        collateralValue: json['collateralValue'].toDouble(),
      );
}

/// Group member model
class GroupMember {
  final String id;
  final String name;
  final String phone;
  final String? email;
  final String role; // 'member', 'leader', 'secretary'
  final DateTime joinDate;
  
  GroupMember({
    required this.id,
    required this.name,
    required this.phone,
    this.email,
    required this.role,
    required this.joinDate,
  }) : assert(name.isNotEmpty, 'Member name cannot be empty'),
       assert(phone.isNotEmpty, 'Member phone cannot be empty');

  /// Check if member is a leader
  bool get isLeader => role == 'leader';

  Map<String, dynamic> toJson() => {
    'id': id,
    'name': name,
    'phone': phone,
    'email': email,
    'role': role,
    'joinDate': joinDate.toIso8601String(),
  };

  factory GroupMember.fromJson(Map<String, dynamic> json) => GroupMember(
    id: json['id'],
    name: json['name'],
    phone: json['phone'],
    email: json['email'],
    role: json['role'],
    joinDate: DateTime.parse(json['joinDate']),
  );
}