import 'dart:async';
import '../models/loan_application.dart';
import '../data/mock_loan_data.dart';
import 'base_loan_service.dart';

/// Mock implementation of loan application service for UI showcase
/// This service simulates API calls with realistic delays and responses
class MockLoanApplicationService extends BaseLoanService {
  static const Duration _simulatedDelay = Duration(milliseconds: 800);
  
  // Simulate network delays for realistic UI behavior
  Future<void> _simulateNetworkDelay() async {
    await Future.delayed(_simulatedDelay);
  }

  /// Fetch all loan applications with optional filtering
  @override
  Future<List<LoanApplication>> fetchApplications({
    LoanStatus? status,
    String? portfolioOfficerId,
    DateTime? fromDate,
    DateTime? toDate,
    int page = 1,
    int limit = 20,
  }) async {
    await _simulateNetworkDelay();
    
    List<LoanApplication> applications = MockLoanData.getLoanApplications();
    
    // Filter by status if provided
    if (status != null) {
      applications = applications.where((app) => app.status == status).toList();
    }
    
    // Filter by portfolio officer if provided
    if (portfolioOfficerId != null) {
      applications = applications.where((app) => app.portfolioOfficer.id == portfolioOfficerId).toList();
    }
    
    // Filter by date range if provided
    if (fromDate != null) {
      applications = applications.where((app) => app.createdAt.isAfter(fromDate)).toList();
    }
    if (toDate != null) {
      applications = applications.where((app) => app.createdAt.isBefore(toDate)).toList();
    }
    
    // Sort by creation date (newest first)
    applications.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    
    // Apply pagination
    final startIndex = (page - 1) * limit;
    final endIndex = (startIndex + limit).clamp(0, applications.length);
    
    return applications.sublist(
      startIndex.clamp(0, applications.length),
      endIndex,
    );
  }

  /// Fetch applications by status
  Future<List<LoanApplication>> fetchApplicationsByStatus(LoanStatus status) async {
    await _simulateNetworkDelay();
    return MockLoanData.getApplicationsByStatus(status);
  }

  /// Fetch applications for specific portfolio officer
  Future<List<LoanApplication>> fetchApplicationsForPO(String poId) async {
    await _simulateNetworkDelay();
    return MockLoanData.getApplicationsForPO(poId);
  }

  /// Fetch single application by ID
  @override
  Future<LoanApplication> fetchApplicationById(String id) async {
    await _simulateNetworkDelay();
    final applications = MockLoanData.getLoanApplications();
    try {
      return applications.firstWhere((app) => app.id == id);
    } catch (e) {
      throw LoanApplicationException('Application not found', 404);
    }
  }

  /// Create new loan application
  @override
  Future<LoanApplication> createApplication(LoanApplication application) async {
    await _simulateNetworkDelay();
    
    // Simulate creating a new application with generated ID
    final newApplication = application.copyWith(
      id: 'APP-${DateTime.now().millisecondsSinceEpoch}',
      status: LoanStatus.pending,
      createdAt: DateTime.now(),
    );
    
    return newApplication;
  }

  /// Update application status (approve/reject)
  Future<LoanApplication> updateApplicationStatus(
    String applicationId,
    LoanStatus newStatus, {
    String? reason,
    String? cpoId,
    String? cpoName,
    String? cpoEmail,
  }) async {
    await _simulateNetworkDelay();
    
    final application = await fetchApplicationById(applicationId);

    CpoApprover? cpoApprover;
    if (newStatus == LoanStatus.approved || newStatus == LoanStatus.rejected) {
      cpoApprover = CpoApprover(
        id: cpoId ?? 'CPO-001',
        name: cpoName ?? 'លោកស្រី ចន្ទ្រា ពេជ្រ',
        email: cpoEmail ?? '<EMAIL>',
        approvalDate: DateTime.now(),
        approvalComments: reason ?? (newStatus == LoanStatus.approved 
            ? 'អនុម័តដោយបំពេញលក្ខខណ្ឌគ្រប់គ្រាន់' 
            : 'បដិសេធដោយមិនបំពេញលក្ខខណ្ឌ'),
      );
    }

    return application.copyWith(
      status: newStatus,
      decisionReason: reason,
      cpoApprover: cpoApprover,
      updatedAt: DateTime.now(),
    );
  }

  /// Disburse loan
  @override
  Future<LoanApplication> disburseLoan(
    String applicationId,
    double amount,
    String disbursementMethod,
  ) async {
    await _simulateNetworkDelay();
    
    final application = await fetchApplicationById(applicationId);

    if (application.status != LoanStatus.approved) {
      throw LoanApplicationException(
        'Cannot disburse loan. Application must be approved first.',
        400,
      );
    }

    return application.copyWith(
      status: LoanStatus.disbursed,
      updatedAt: DateTime.now(),
    );
  }

  /// Get dashboard statistics
  Future<Map<String, dynamic>> getDashboardStats() async {
    await _simulateNetworkDelay();
    return MockLoanData.getDashboardStats();
  }

  /// Update an existing loan application
  @override
  Future<LoanApplication> updateApplication(LoanApplication application) async {
    await _simulateNetworkDelay();
    return application.copyWith(updatedAt: DateTime.now());
  }

  /// Approve a loan application
  @override
  Future<LoanApplication> approveApplication(
    String applicationId,
    String approverId,
    String? comments,
  ) async {
    return updateApplicationStatus(
      applicationId,
      LoanStatus.approved,
      reason: comments,
      cpoId: approverId,
    );
  }

  /// Reject a loan application
  @override
  Future<LoanApplication> rejectApplication(
    String applicationId,
    String reason,
  ) async {
    return updateApplicationStatus(
      applicationId,
      LoanStatus.rejected,
      reason: reason,
    );
  }

  /// Get application statistics
  @override
  Future<ApplicationStatistics> getApplicationStatistics() async {
    await _simulateNetworkDelay();
    final stats = MockLoanData.getDashboardStats();
    
    return ApplicationStatistics(
      totalApplications: stats['totalApplications'] ?? 0,
      pendingApplications: stats['pendingApplications'] ?? 0,
      approvedApplications: stats['approvedApplications'] ?? 0,
      rejectedApplications: stats['rejectedApplications'] ?? 0,
      disbursedApplications: stats['disbursedApplications'] ?? 0,
      totalDisbursedAmount: (stats['totalDisbursedAmount'] ?? 0.0).toDouble(),
      averageProcessingTime: (stats['averageProcessingTime'] ?? 0.0).toDouble(),
      approvalRate: (stats['approvalRate'] ?? 0.0).toDouble(),
    );
  }

  /// Get portfolio officers
  @override
  Future<List<PortfolioOfficer>> getPortfolioOfficers() async {
    await _simulateNetworkDelay();
    return MockLoanData.getPortfolioOfficers();
  }

  /// Upload document (simulated)
  @override
  Future<String> uploadDocument(
    String applicationId,
    String documentType,
    String filePath,
  ) async {
    await _simulateNetworkDelay();
    
    // Simulate successful upload and return mock URL
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return 'https://example.com/docs/${applicationId}_${documentType}_$timestamp.jpg';
  }

  /// Search applications
  @override
  Future<List<LoanApplication>> searchApplications(String query) async {
    await _simulateNetworkDelay();
    
    final applications = MockLoanData.getLoanApplications();
    final lowercaseQuery = query.toLowerCase();
    
    return applications.where((app) {
      return app.applicant.name.toLowerCase().contains(lowercaseQuery) ||
             app.id.toLowerCase().contains(lowercaseQuery) ||
             app.business.name.toLowerCase().contains(lowercaseQuery) ||
             app.business.type.toLowerCase().contains(lowercaseQuery);
    }).toList();
  }

  /// Get applications with pagination
  Future<Map<String, dynamic>> getApplicationsPaginated({
    int page = 1,
    int limit = 10,
    LoanStatus? status,
    String? poId,
  }) async {
    await _simulateNetworkDelay();
    
    List<LoanApplication> applications = MockLoanData.getLoanApplications();
    
    // Filter by status if provided
    if (status != null) {
      applications = applications.where((app) => app.status == status).toList();
    }
    
    // Filter by portfolio officer if provided
    if (poId != null) {
      applications = applications.where((app) => app.portfolioOfficer.id == poId).toList();
    }
    
    // Sort by creation date (newest first)
    applications.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    
    final totalCount = applications.length;
    final totalPages = (totalCount / limit).ceil();
    final startIndex = (page - 1) * limit;
    final endIndex = (startIndex + limit).clamp(0, totalCount);
    
    final paginatedApplications = applications.sublist(
      startIndex.clamp(0, totalCount),
      endIndex,
    );
    
    return {
      'applications': paginatedApplications,
      'pagination': {
        'currentPage': page,
        'totalPages': totalPages,
        'totalCount': totalCount,
        'hasNextPage': page < totalPages,
        'hasPreviousPage': page > 1,
      },
    };
  }

  /// Simulate loan analysis (AI feature)
  Future<Map<String, dynamic>> analyzeLoanApplication(String applicationId) async {
    await Future.delayed(const Duration(seconds: 2)); // Longer delay for AI analysis
    
    final application = await fetchApplicationById(applicationId);

    // Simulate AI analysis results
    final riskScore = _calculateRiskScore(application);
    final recommendation = _getRecommendation(riskScore);
    
    return {
      'applicationId': applicationId,
      'riskScore': riskScore,
      'riskLevel': _getRiskLevel(riskScore),
      'recommendation': recommendation,
      'factors': _getAnalysisFactors(application),
      'suggestedAmount': _getSuggestedAmount(application, riskScore),
      'suggestedTerm': _getSuggestedTerm(application, riskScore),
      'analysisDate': DateTime.now().toIso8601String(),
    };
  }

  // Helper methods for AI analysis simulation
  double _calculateRiskScore(LoanApplication application) {
    double score = 50.0; // Base score
    
    // Business experience factor
    if (application.business.yearsInOperation >= 3) {
      score += 15;
    } else if (application.business.yearsInOperation >= 1) score += 5;
    else score -= 10;
    
    // Income stability factor
    final incomeRatio = application.business.monthlyIncome / application.requestedAmount;
    if (incomeRatio >= 0.3) {
      score += 20;
    } else if (incomeRatio >= 0.2) score += 10;
    else score -= 15;
    
    // Asset coverage factor
    final assetCoverage = application.assets.totalValue / application.requestedAmount;
    if (assetCoverage >= 2.0) {
      score += 15;
    } else if (assetCoverage >= 1.5) score += 10;
    else if (assetCoverage >= 1.0) score += 5;
    else score -= 10;
    
    // Family stability factor
    if (application.family.numberOfChildren <= 2) {
      score += 5;
    } else {
      score -= 5;
    }
    
    return score.clamp(0.0, 100.0);
  }

  String _getRiskLevel(double score) {
    if (score >= 80) return 'ទាប'; // Low
    if (score >= 60) return 'មធ្យម'; // Medium
    if (score >= 40) return 'ខ្ពស់'; // High
    return 'ខ្ពស់ណាស់'; // Very High
  }

  String _getRecommendation(double score) {
    if (score >= 75) return 'អនុម័តដោយមិនមានលក្ខខណ្ឌ';
    if (score >= 60) return 'អនុម័តជាមួយលក្ខខណ្ឌ';
    if (score >= 40) return 'ត្រូវការការវិភាគបន្ថែម';
    return 'មិនអនុម័ត';
  }

  List<Map<String, dynamic>> _getAnalysisFactors(LoanApplication application) {
    return [
      {
        'factor': 'បទពិសោធន៍អាជីវកម្ម',
        'value': '${application.business.yearsInOperation} ឆ្នាំ',
        'impact': application.business.yearsInOperation >= 2 ? 'វិជ្ជមាន' : 'អវិជ្ជមាន',
      },
      {
        'factor': 'ចំណូលប្រចាំខែ',
        'value': '${(application.business.monthlyIncome / 1000).toStringAsFixed(0)}K KHR',
        'impact': application.business.monthlyIncome >= 1000000 ? 'វិជ្ជមាន' : 'អវិជ្ជមាន',
      },
      {
        'factor': 'តម្លៃទ្រព្យសម្បត្តិ',
        'value': '${(application.assets.totalValue / 1000000).toStringAsFixed(1)}M KHR',
        'impact': application.assets.totalValue >= application.requestedAmount ? 'វិជ្ជមាន' : 'អវិជ្ជមាន',
      },
      {
        'factor': 'ចំនួនកូន',
        'value': '${application.family.numberOfChildren} នាក់',
        'impact': application.family.numberOfChildren <= 2 ? 'វិជ្ជមាន' : 'អវិជ្ជមាន',
      },
    ];
  }

  double _getSuggestedAmount(LoanApplication application, double riskScore) {
    if (riskScore >= 75) return application.requestedAmount;
    if (riskScore >= 60) return application.requestedAmount * 0.8;
    if (riskScore >= 40) return application.requestedAmount * 0.6;
    return application.requestedAmount * 0.4;
  }

  int _getSuggestedTerm(LoanApplication application, double riskScore) {
    if (riskScore >= 75) return application.loanTerm;
    if (riskScore >= 60) return (application.loanTerm * 0.8).round();
    return (application.loanTerm * 0.6).round();
  }
}

/// Exception class for mock service errors
class LoanApplicationException implements Exception {
  final String message;
  final int? statusCode;
  
  LoanApplicationException(this.message, [this.statusCode]);
  
  @override
  String toString() => 'LoanApplicationException: $message';
}