import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../utils/colors.dart';
import '../widgets/ui_components.dart';
import '../providers/loan_application_provider.dart';
import '../models/loan_application.dart';
import '../main.dart';

/// Modern loan details screen with comprehensive information display
class LoanDetailsScreen extends ConsumerStatefulWidget {
  final String applicationId;

  const LoanDetailsScreen({super.key, required this.applicationId});

  @override
  ConsumerState<LoanDetailsScreen> createState() => _LoanDetailsScreenState();
}

class _LoanDetailsScreenState extends ConsumerState<LoanDetailsScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  
  final ScrollController _scrollController = ScrollController();
  bool _showFloatingButton = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _setupScrollListener();
    _loadApplicationDetails();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));
    
    _animationController.forward();
  }

  void _setupScrollListener() {
    _scrollController.addListener(() {
      final shouldShow = _scrollController.offset > 200;
      if (shouldShow != _showFloatingButton) {
        setState(() {
          _showFloatingButton = shouldShow;
        });
      }
    });
  }

  void _loadApplicationDetails() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(loanApplicationsProvider.notifier).fetchApplicationById(widget.applicationId);
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final applicationsState = ref.watch(loanApplicationsProvider);
    final application = applicationsState.applications
        .where((app) => app.id == widget.applicationId)
        .firstOrNull;

    return Scaffold(
      backgroundColor: AppColors.background,
      body: _buildBody(application, applicationsState.isLoading, applicationsState.error),
      floatingActionButton: _buildFloatingActionButton(application),
    );
  }

  Widget _buildBody(LoanApplication? application, bool isLoading, String? error) {
    if (isLoading) {
      return const Center(
        child: CircularProgressIndicator(color: AppColors.primary),
      );
    }

    if (error != null) {
      return _ErrorView(error: error, onRetry: _loadApplicationDetails);
    }

    if (application == null) {
      return _NotFoundView();
    }

    return CustomScrollView(
      controller: _scrollController,
      slivers: [
        _buildAppBar(application),
        SliverPadding(
          padding: const EdgeInsets.all(16.0),
          sliver: SliverList(
            delegate: SliverChildListDelegate([
              _buildStatusCard(application),
              const SizedBox(height: 16),
              _buildApplicationSummary(application),
              const SizedBox(height: 16),
              _buildPersonalInformation(application),
              const SizedBox(height: 16),
              _buildLoanInformation(application),
              const SizedBox(height: 16),
              _buildFinancialInformation(application),
              const SizedBox(height: 16),
              _buildDocumentsSection(application),
              const SizedBox(height: 16),
              _buildTimelineSection(application),
              const SizedBox(height: 100), // Space for FAB
            ]),
          ),
        ),
      ],
    );
  }

  Widget _buildAppBar(LoanApplication application) {
    return SliverAppBar(
      expandedHeight: 120,
      floating: false,
      pinned: true,
      backgroundColor: _getStatusColor(application.status),
      flexibleSpace: FlexibleSpaceBar(
        title: Text(
          'Application #${application.id.substring(0, 8)}',
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                _getStatusColor(application.status),
                _getStatusColor(application.status).withOpacity(0.8),
              ],
            ),
          ),
        ),
      ),
      leading: IconButton(
        icon: const Icon(Icons.arrow_back, color: Colors.white),
        onPressed: () => Navigator.of(context).pop(),
      ),
      actions: [
        _ActionMenuButton(application: application),
        const SizedBox(width: 8),
      ],
    );
  }

  Widget _buildStatusCard(LoanApplication application) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: _StatusCard(application: application),
      ),
    );
  }

  Widget _buildApplicationSummary(LoanApplication application) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: Tween<Offset>(
          begin: const Offset(0, 0.4),
          end: Offset.zero,
        ).animate(CurvedAnimation(
          parent: _animationController,
          curve: const Interval(0.2, 1.0, curve: Curves.easeOutCubic),
        )),
        child: _ApplicationSummaryCard(application: application),
      ),
    );
  }

  Widget _buildPersonalInformation(LoanApplication application) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: Tween<Offset>(
          begin: const Offset(0, 0.5),
          end: Offset.zero,
        ).animate(CurvedAnimation(
          parent: _animationController,
          curve: const Interval(0.3, 1.0, curve: Curves.easeOutCubic),
        )),
        child: _PersonalInformationCard(application: application),
      ),
    );
  }

  Widget _buildLoanInformation(LoanApplication application) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: Tween<Offset>(
          begin: const Offset(0, 0.6),
          end: Offset.zero,
        ).animate(CurvedAnimation(
          parent: _animationController,
          curve: const Interval(0.4, 1.0, curve: Curves.easeOutCubic),
        )),
        child: _LoanInformationCard(application: application),
      ),
    );
  }

  Widget _buildFinancialInformation(LoanApplication application) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: Tween<Offset>(
          begin: const Offset(0, 0.7),
          end: Offset.zero,
        ).animate(CurvedAnimation(
          parent: _animationController,
          curve: const Interval(0.5, 1.0, curve: Curves.easeOutCubic),
        )),
        child: _FinancialInformationCard(application: application),
      ),
    );
  }

  Widget _buildDocumentsSection(LoanApplication application) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: Tween<Offset>(
          begin: const Offset(0, 0.8),
          end: Offset.zero,
        ).animate(CurvedAnimation(
          parent: _animationController,
          curve: const Interval(0.6, 1.0, curve: Curves.easeOutCubic),
        )),
        child: _DocumentsCard(application: application),
      ),
    );
  }

  Widget _buildTimelineSection(LoanApplication application) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: Tween<Offset>(
          begin: const Offset(0, 0.9),
          end: Offset.zero,
        ).animate(CurvedAnimation(
          parent: _animationController,
          curve: const Interval(0.7, 1.0, curve: Curves.easeOutCubic),
        )),
        child: _TimelineCard(application: application),
      ),
    );
  }

  Widget _buildFloatingActionButton(LoanApplication? application) {
    if (application == null) return const SizedBox.shrink();

    return AnimatedScale(
      scale: _showFloatingButton ? 1.0 : 0.0,
      duration: const Duration(milliseconds: 200),
      child: FloatingActionButton.extended(
        onPressed: () => _showActionBottomSheet(application),
        backgroundColor: AppColors.secondary,
        icon: const Icon(Icons.more_horiz, color: Colors.white),
        label: const Text(
          'Actions',
          style: TextStyle(color: Colors.white, fontWeight: FontWeight.w600),
        ),
      ),
    );
  }

  Color _getStatusColor(LoanStatus status) {
    switch (status) {
      case LoanStatus.pending:
        return AppColors.pending;
      case LoanStatus.approved:
        return AppColors.approved;
      case LoanStatus.rejected:
        return AppColors.rejected;
      case LoanStatus.disbursed:
        return AppColors.secondary;
    }
  }

  void _showActionBottomSheet(LoanApplication application) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => _ActionBottomSheet(application: application),
    );
  }
}

/// Status card component
class _StatusCard extends StatelessWidget {
  final LoanApplication application;

  const _StatusCard({required this.application});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            _getStatusColor(application.status).withOpacity(0.1),
            _getStatusColor(application.status).withOpacity(0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: _getStatusColor(application.status).withOpacity(0.3),
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: _getStatusColor(application.status).withOpacity(0.2),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Icon(
              _getStatusIcon(application.status),
              color: _getStatusColor(application.status),
              size: 30,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _getStatusText(application.status),
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    color: _getStatusColor(application.status),
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  _getStatusDescription(application.status),
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppColors.textPrimary.withOpacity(0.7),
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Applied on ${AppUtils.formatDate(application.applicationDate)}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.textPrimary.withOpacity(0.5),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(LoanStatus status) {
    switch (status) {
      case LoanStatus.pending:
        return AppColors.pending;
      case LoanStatus.approved:
        return AppColors.approved;
      case LoanStatus.rejected:
        return AppColors.rejected;
      case LoanStatus.disbursed:
        return AppColors.secondary;
    }
  }

  IconData _getStatusIcon(LoanStatus status) {
    switch (status) {
      case LoanStatus.pending:
        return Icons.pending_outlined;
      case LoanStatus.approved:
        return Icons.check_circle_outline;
      case LoanStatus.rejected:
        return Icons.cancel_outlined;
      case LoanStatus.disbursed:
        return Icons.account_balance_wallet_outlined;
    }
  }

  String _getStatusText(LoanStatus status) {
    switch (status) {
      case LoanStatus.pending:
        return 'Under Review';
      case LoanStatus.approved:
        return 'Approved';
      case LoanStatus.rejected:
        return 'Rejected';
      case LoanStatus.disbursed:
        return 'Disbursed';
    }
  }

  String _getStatusDescription(LoanStatus status) {
    switch (status) {
      case LoanStatus.pending:
        return 'Your application is being reviewed by our team';
      case LoanStatus.approved:
        return 'Congratulations! Your loan has been approved';
      case LoanStatus.rejected:
        return 'Unfortunately, your application was not approved';
      case LoanStatus.disbursed:
        return 'Loan amount has been disbursed to your account';
    }
  }
}

/// Application summary card component
class _ApplicationSummaryCard extends StatelessWidget {
  final LoanApplication application;

  const _ApplicationSummaryCard({required this.application});

  @override
  Widget build(BuildContext context) {
    return _InfoCard(
      title: 'Application Summary',
      icon: Icons.summarize_outlined,
      child: Column(
        children: [
          _SummaryRow(
            label: 'Loan Amount',
            value: AppUtils.formatCurrency(application.requestedAmount),
            isHighlighted: true,
          ),
          _SummaryRow(
            label: 'Loan Type',
            value: _getLoanTypeDisplayName(application.loanType),
          ),
          _SummaryRow(
            label: 'Application ID',
            value: '#${application.id.substring(0, 8)}',
          ),
          _SummaryRow(
            label: 'Application Date',
            value: AppUtils.formatDate(application.applicationDate),
          ),
        ],
      ),
    );
  }

  String _getLoanTypeDisplayName(LoanType type) {
    switch (type) {
      case LoanType.personal:
        return 'Personal Loan';
      case LoanType.business:
        return 'Business Loan';
      case LoanType.home:
        return 'Home Loan';
      case LoanType.education:
        return 'Education Loan';
      case LoanType.vehicle:
        return 'Vehicle Loan';
    }
  }
}

/// Personal information card component
class _PersonalInformationCard extends StatelessWidget {
  final LoanApplication application;

  const _PersonalInformationCard({required this.application});

  @override
  Widget build(BuildContext context) {
    return _InfoCard(
      title: 'Personal Information',
      icon: Icons.person_outline,
      child: Column(
        children: [
          _InfoRow(
            label: 'Full Name',
            value: application.applicantName,
          ),
          _InfoRow(
            label: 'Email',
            value: application.email,
          ),
          _InfoRow(
            label: 'Phone Number',
            value: application.phoneNumber,
          ),
          _InfoRow(
            label: 'Address',
            value: application.address,
          ),
        ],
      ),
    );
  }
}

/// Loan information card component
class _LoanInformationCard extends StatelessWidget {
  final LoanApplication application;

  const _LoanInformationCard({required this.application});

  @override
  Widget build(BuildContext context) {
    return _InfoCard(
      title: 'Loan Information',
      icon: Icons.account_balance_outlined,
      child: Column(
        children: [
          _InfoRow(
            label: 'Requested Amount',
            value: AppUtils.formatCurrency(application.requestedAmount),
          ),
          _InfoRow(
            label: 'Loan Type',
            value: _getLoanTypeDisplayName(application.loanType),
          ),
          _InfoRow(
            label: 'Purpose',
            value: application.purpose,
          ),
          if (application.interestRate != null)
            _InfoRow(
              label: 'Interest Rate',
              value: '${application.interestRate!.toStringAsFixed(2)}% per annum',
            ),
          if (application.loanTerm != null)
            _InfoRow(
              label: 'Loan Term',
              value: '${application.loanTerm} months',
            ),
        ],
      ),
    );
  }

  String _getLoanTypeDisplayName(LoanType type) {
    switch (type) {
      case LoanType.personal:
        return 'Personal Loan';
      case LoanType.business:
        return 'Business Loan';
      case LoanType.home:
        return 'Home Loan';
      case LoanType.education:
        return 'Education Loan';
      case LoanType.vehicle:
        return 'Vehicle Loan';
    }
  }
}

/// Financial information card component
class _FinancialInformationCard extends StatelessWidget {
  final LoanApplication application;

  const _FinancialInformationCard({required this.application});

  @override
  Widget build(BuildContext context) {
    return _InfoCard(
      title: 'Financial Information',
      icon: Icons.monetization_on_outlined,
      child: Column(
        children: [
          _InfoRow(
            label: 'Monthly Income',
            value: AppUtils.formatCurrency(application.monthlyIncome),
          ),
          _InfoRow(
            label: 'Employment Status',
            value: application.employmentStatus,
          ),
          if (application.creditScore != null)
            _InfoRow(
              label: 'Credit Score',
              value: application.creditScore.toString(),
            ),
        ],
      ),
    );
  }
}

/// Documents card component
class _DocumentsCard extends StatelessWidget {
  final LoanApplication application;

  const _DocumentsCard({required this.application});

  @override
  Widget build(BuildContext context) {
    return _InfoCard(
      title: 'Documents',
      icon: Icons.description_outlined,
      child: application.documents.isEmpty
          ? _EmptyDocumentsView()
          : Column(
              children: application.documents.map((document) {
                return _DocumentItem(documentName: document);
              }).toList(),
            ),
    );
  }
}

/// Timeline card component
class _TimelineCard extends StatelessWidget {
  final LoanApplication application;

  const _TimelineCard({required this.application});

  @override
  Widget build(BuildContext context) {
    final timelineEvents = _generateTimelineEvents();
    
    return _InfoCard(
      title: 'Application Timeline',
      icon: Icons.timeline_outlined,
      child: Column(
        children: timelineEvents.map((event) {
          return _TimelineItem(
            title: event['title'],
            date: event['date'],
            isCompleted: event['isCompleted'],
            isLast: event == timelineEvents.last,
          );
        }).toList(),
      ),
    );
  }

  List<Map<String, dynamic>> _generateTimelineEvents() {
    final events = <Map<String, dynamic>>[
      {
        'title': 'Application Submitted',
        'date': AppUtils.formatDate(application.applicationDate),
        'isCompleted': true,
      },
      {
        'title': 'Document Verification',
        'date': application.status != LoanStatus.pending ? 'Completed' : 'In Progress',
        'isCompleted': application.status != LoanStatus.pending,
      },
      {
        'title': 'Credit Assessment',
        'date': application.status == LoanStatus.approved || application.status == LoanStatus.disbursed
            ? 'Completed'
            : application.status == LoanStatus.rejected
                ? 'Completed'
                : 'Pending',
        'isCompleted': application.status != LoanStatus.pending,
      },
    ];

    if (application.status == LoanStatus.approved || application.status == LoanStatus.disbursed) {
      events.add({
        'title': 'Loan Approved',
        'date': 'Completed',
        'isCompleted': true,
      });
    }

    if (application.status == LoanStatus.disbursed) {
      events.add({
        'title': 'Amount Disbursed',
        'date': 'Completed',
        'isCompleted': true,
      });
    }

    if (application.status == LoanStatus.rejected) {
      events.add({
        'title': 'Application Rejected',
        'date': 'Completed',
        'isCompleted': true,
      });
    }

    return events;
  }
}

/// Info card wrapper component
class _InfoCard extends StatelessWidget {
  final String title;
  final IconData icon;
  final Widget child;

  const _InfoCard({
    required this.title,
    required this.icon,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: AppColors.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(
                  icon,
                  color: AppColors.primary,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                title,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: AppColors.textPrimary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          child,
        ],
      ),
    );
  }
}

/// Summary row component
class _SummaryRow extends StatelessWidget {
  final String label;
  final String value;
  final bool isHighlighted;

  const _SummaryRow({
    required this.label,
    required this.value,
    this.isHighlighted = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        vertical: isHighlighted ? 12 : 8,
        horizontal: isHighlighted ? 16 : 0,
      ),
      margin: const EdgeInsets.only(bottom: 8),
      decoration: isHighlighted
          ? BoxDecoration(
              color: AppColors.primary.withOpacity(0.05),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: AppColors.primary.withOpacity(0.2),
              ),
            )
          : null,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.textPrimary.withOpacity(0.7),
              fontWeight: isHighlighted ? FontWeight.w600 : FontWeight.normal,
            ),
          ),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: isHighlighted ? AppColors.primary : AppColors.textPrimary,
              fontWeight: FontWeight.w600,
              fontSize: isHighlighted ? 16 : 14,
            ),
          ),
        ],
      ),
    );
  }
}

/// Info row component
class _InfoRow extends StatelessWidget {
  final String label;
  final String value;

  const _InfoRow({
    required this.label,
    required this.value,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.textPrimary.withOpacity(0.7),
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.textPrimary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// Document item component
class _DocumentItem extends StatelessWidget {
  final String documentName;

  const _DocumentItem({required this.documentName});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: AppColors.approved.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: AppColors.approved.withOpacity(0.3),
        ),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.check_circle,
            color: AppColors.approved,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              documentName,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.textPrimary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          IconButton(
            icon: const Icon(
              Icons.download_outlined,
              color: AppColors.primary,
              size: 20,
            ),
            onPressed: () {
              // TODO: Implement document download
              AppUtils.showSnackBar(context, 'Download feature coming soon!');
            },
          ),
        ],
      ),
    );
  }
}

/// Empty documents view component
class _EmptyDocumentsView extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          Icon(
            Icons.description_outlined,
            size: 48,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 12),
          Text(
            'No documents uploaded',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ),
    );
  }
}

/// Timeline item component
class _TimelineItem extends StatelessWidget {
  final String title;
  final String date;
  final bool isCompleted;
  final bool isLast;

  const _TimelineItem({
    required this.title,
    required this.date,
    required this.isCompleted,
    required this.isLast,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Column(
          children: [
            Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                color: isCompleted ? AppColors.approved : Colors.grey.shade300,
                shape: BoxShape.circle,
              ),
              child: isCompleted
                  ? const Icon(
                      Icons.check,
                      color: Colors.white,
                      size: 12,
                    )
                  : null,
            ),
            if (!isLast)
              Container(
                width: 2,
                height: 40,
                color: isCompleted ? AppColors.approved : Colors.grey.shade300,
              ),
          ],
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppColors.textPrimary,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                date,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppColors.textPrimary.withOpacity(0.6),
                ),
              ),
              if (!isLast) const SizedBox(height: 16),
            ],
          ),
        ),
      ],
    );
  }
}

/// Action menu button component
class _ActionMenuButton extends StatelessWidget {
  final LoanApplication application;

  const _ActionMenuButton({required this.application});

  @override
  Widget build(BuildContext context) {
    return PopupMenuButton<String>(
      icon: const Icon(Icons.more_vert, color: Colors.white),
      onSelected: (value) => _handleMenuAction(context, value),
      itemBuilder: (context) => [
        const PopupMenuItem(
          value: 'share',
          child: Row(
            children: [
              Icon(Icons.share, size: 20),
              SizedBox(width: 12),
              Text('Share'),
            ],
          ),
        ),
        const PopupMenuItem(
          value: 'download',
          child: Row(
            children: [
              Icon(Icons.download, size: 20),
              SizedBox(width: 12),
              Text('Download PDF'),
            ],
          ),
        ),
        if (application.status == LoanStatus.pending)
          const PopupMenuItem(
            value: 'edit',
            child: Row(
              children: [
                Icon(Icons.edit, size: 20),
                SizedBox(width: 12),
                Text('Edit Application'),
              ],
            ),
          ),
        if (application.status == LoanStatus.pending)
          const PopupMenuItem(
            value: 'cancel',
            child: Row(
              children: [
                Icon(Icons.cancel, size: 20, color: Colors.red),
                SizedBox(width: 12),
                Text('Cancel Application', style: TextStyle(color: Colors.red)),
              ],
            ),
          ),
      ],
    );
  }

  void _handleMenuAction(BuildContext context, String action) {
    switch (action) {
      case 'share':
        // TODO: Implement share functionality
        AppUtils.showSnackBar(context, 'Share feature coming soon!');
        break;
      case 'download':
        // TODO: Implement PDF download
        AppUtils.showSnackBar(context, 'Download feature coming soon!');
        break;
      case 'edit':
        // TODO: Navigate to edit screen
        AppUtils.showSnackBar(context, 'Edit feature coming soon!');
        break;
      case 'cancel':
        _showCancelConfirmation(context);
        break;
    }
  }

  void _showCancelConfirmation(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Cancel Application'),
        content: const Text(
          'Are you sure you want to cancel this loan application? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Keep Application'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: Implement cancel functionality
              AppUtils.showSnackBar(context, 'Cancel feature coming soon!');
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Cancel Application'),
          ),
        ],
      ),
    );
  }
}

/// Action bottom sheet component
class _ActionBottomSheet extends StatelessWidget {
  final LoanApplication application;

  const _ActionBottomSheet({required this.application});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.symmetric(vertical: 12),
            decoration: BoxDecoration(
              color: Colors.grey.shade300,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Quick Actions',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 20),
                _ActionButton(
                  icon: Icons.share,
                  title: 'Share Application',
                  subtitle: 'Share application details',
                  onTap: () {
                    Navigator.of(context).pop();
                    AppUtils.showSnackBar(context, 'Share feature coming soon!');
                  },
                ),
                _ActionButton(
                  icon: Icons.download,
                  title: 'Download PDF',
                  subtitle: 'Download application as PDF',
                  onTap: () {
                    Navigator.of(context).pop();
                    AppUtils.showSnackBar(context, 'Download feature coming soon!');
                  },
                ),
                if (application.status == LoanStatus.pending)
                  _ActionButton(
                    icon: Icons.edit,
                    title: 'Edit Application',
                    subtitle: 'Modify application details',
                    onTap: () {
                      Navigator.of(context).pop();
                      AppUtils.showSnackBar(context, 'Edit feature coming soon!');
                    },
                  ),
                const SizedBox(height: 20),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// Action button component
class _ActionButton extends StatelessWidget {
  final IconData icon;
  final String title;
  final String subtitle;
  final VoidCallback onTap;

  const _ActionButton({
    required this.icon,
    required this.title,
    required this.subtitle,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        margin: const EdgeInsets.only(bottom: 8),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey.shade200),
        ),
        child: Row(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: AppColors.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Icon(
                icon,
                color: AppColors.primary,
                size: 20,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    subtitle,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),
            const Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: Colors.grey,
            ),
          ],
        ),
      ),
    );
  }
}

/// Error view component
class _ErrorView extends StatelessWidget {
  final String error;
  final VoidCallback onRetry;

  const _ErrorView({
    required this.error,
    required this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              'Something went wrong',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: Colors.red.shade700,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              error,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey.shade600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: onRetry,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
              ),
              child: const Text('Try Again'),
            ),
          ],
        ),
      ),
    );
  }
}

/// Not found view component
class _NotFoundView extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 64,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              'Application Not Found',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: Colors.grey.shade700,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'The requested loan application could not be found.',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey.shade600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
              ),
              child: const Text('Go Back'),
            ),
          ],
        ),
      ),
    );
  }
}