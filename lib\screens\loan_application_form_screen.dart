import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../utils/colors.dart';
import '../widgets/ui_components.dart';
import '../providers/loan_application_provider.dart';
import '../models/loan_application.dart';
import '../main.dart';

/// Modern multi-step loan application form screen
class LoanApplicationFormScreen extends ConsumerStatefulWidget {
  const LoanApplicationFormScreen({super.key});

  @override
  ConsumerState<LoanApplicationFormScreen> createState() => _LoanApplicationFormScreenState();
}

class _LoanApplicationFormScreenState extends ConsumerState<LoanApplicationFormScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  
  final PageController _pageController = PageController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  
  int _currentStep = 0;
  final int _totalSteps = 4;
  bool _isSubmitting = false;
  
  // Form data
  final Map<String, dynamic> _formData = {
    'personalInfo': {},
    'loanDetails': {},
    'financialInfo': {},
    'documents': {},
  };

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));
    
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: _buildAppBar(),
      body: _buildBody(),
      bottomNavigationBar: _buildBottomNavigation(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: Colors.white,
      elevation: 0,
      leading: IconButton(
        icon: const Icon(Icons.arrow_back, color: AppColors.textPrimary),
        onPressed: () => Navigator.of(context).pop(),
      ),
      title: Text(
        'Loan Application',
        style: Theme.of(context).textTheme.titleLarge?.copyWith(
          color: AppColors.textPrimary,
          fontWeight: FontWeight.bold,
        ),
      ),
      bottom: PreferredSize(
        preferredSize: const Size.fromHeight(80),
        child: _ProgressIndicator(currentStep: _currentStep, totalSteps: _totalSteps),
      ),
    );
  }

  Widget _buildBody() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: Form(
          key: _formKey,
          child: PageView(
            controller: _pageController,
            physics: const NeverScrollableScrollPhysics(),
            children: [
              _PersonalInfoStep(formData: _formData['personalInfo']),
              _LoanDetailsStep(formData: _formData['loanDetails']),
              _FinancialInfoStep(formData: _formData['financialInfo']),
              _DocumentsStep(formData: _formData['documents']),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBottomNavigation() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          if (_currentStep > 0)
            Expanded(
              child: _SecondaryButton(
                text: 'Previous',
                onPressed: _previousStep,
              ),
            ),
          if (_currentStep > 0) const SizedBox(width: 16),
          Expanded(
            child: _PrimaryButton(
              text: _currentStep == _totalSteps - 1 ? 'Submit' : 'Next',
              onPressed: _isSubmitting ? null : _nextStep,
              isLoading: _isSubmitting,
            ),
          ),
        ],
      ),
    );
  }

  void _nextStep() {
    if (_validateCurrentStep()) {
      if (_currentStep < _totalSteps - 1) {
        setState(() {
          _currentStep++;
        });
        _pageController.nextPage(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      } else {
        _submitApplication();
      }
    }
  }

  void _previousStep() {
    if (_currentStep > 0) {
      setState(() {
        _currentStep--;
      });
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  bool _validateCurrentStep() {
    // TODO: Implement step-specific validation
    return _formKey.currentState?.validate() ?? false;
  }

  Future<void> _submitApplication() async {
    setState(() {
      _isSubmitting = true;
    });

    try {
      // Create loan application from form data
      final application = LoanApplication(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        applicantName: _formData['personalInfo']['fullName'] ?? '',
        requestedAmount: double.tryParse(_formData['loanDetails']['amount'] ?? '0') ?? 0.0,
        loanType: _formData['loanDetails']['type'] ?? LoanType.personal,
        status: LoanStatus.pending,
        applicationDate: DateTime.now(),
        phoneNumber: _formData['personalInfo']['phone'] ?? '',
        email: _formData['personalInfo']['email'] ?? '',
        address: _formData['personalInfo']['address'] ?? '',
        monthlyIncome: double.tryParse(_formData['financialInfo']['monthlyIncome'] ?? '0') ?? 0.0,
        employmentStatus: _formData['financialInfo']['employmentStatus'] ?? '',
        purpose: _formData['loanDetails']['purpose'] ?? '',
        documents: _formData['documents']['uploadedFiles'] ?? [],
      );

      await ref.read(loanApplicationsProvider.notifier).createApplication(application);
      
      if (mounted) {
        AppUtils.showSnackBar(context, 'Application submitted successfully!');
        Navigator.of(context).pushReplacementNamed('/dashboard');
      }
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(context, 'Failed to submit application: $e');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }
}

/// Progress indicator component
class _ProgressIndicator extends StatelessWidget {
  final int currentStep;
  final int totalSteps;

  const _ProgressIndicator({
    required this.currentStep,
    required this.totalSteps,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Row(
            children: List.generate(totalSteps, (index) {
              final isCompleted = index < currentStep;
              final isCurrent = index == currentStep;
              
              return Expanded(
                child: Row(
                  children: [
                    Expanded(
                      child: Container(
                        height: 4,
                        decoration: BoxDecoration(
                          color: isCompleted || isCurrent
                              ? AppColors.primary
                              : AppColors.primary.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(2),
                        ),
                      ),
                    ),
                    if (index < totalSteps - 1) const SizedBox(width: 8),
                  ],
                ),
              );
            }),
          ),
          const SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Step ${currentStep + 1} of $totalSteps',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppColors.textPrimary.withOpacity(0.7),
                ),
              ),
              Text(
                _getStepTitle(currentStep),
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppColors.primary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  String _getStepTitle(int step) {
    switch (step) {
      case 0:
        return 'Personal Information';
      case 1:
        return 'Loan Details';
      case 2:
        return 'Financial Information';
      case 3:
        return 'Documents';
      default:
        return '';
    }
  }
}

/// Personal information step component
class _PersonalInfoStep extends StatefulWidget {
  final Map<String, dynamic> formData;

  const _PersonalInfoStep({required this.formData});

  @override
  State<_PersonalInfoStep> createState() => _PersonalInfoStepState();
}

class _PersonalInfoStepState extends State<_PersonalInfoStep> {
  final _fullNameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _addressController = TextEditingController();
  final _dateOfBirthController = TextEditingController();
  final _nidController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadExistingData();
  }

  void _loadExistingData() {
    _fullNameController.text = widget.formData['fullName'] ?? '';
    _emailController.text = widget.formData['email'] ?? '';
    _phoneController.text = widget.formData['phone'] ?? '';
    _addressController.text = widget.formData['address'] ?? '';
    _dateOfBirthController.text = widget.formData['dateOfBirth'] ?? '';
    _nidController.text = widget.formData['nid'] ?? '';
  }

  @override
  void dispose() {
    _saveFormData();
    _fullNameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _addressController.dispose();
    _dateOfBirthController.dispose();
    _nidController.dispose();
    super.dispose();
  }

  void _saveFormData() {
    widget.formData['fullName'] = _fullNameController.text;
    widget.formData['email'] = _emailController.text;
    widget.formData['phone'] = _phoneController.text;
    widget.formData['address'] = _addressController.text;
    widget.formData['dateOfBirth'] = _dateOfBirthController.text;
    widget.formData['nid'] = _nidController.text;
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _StepHeader(
            title: 'Personal Information',
            subtitle: 'Please provide your personal details',
          ),
          const SizedBox(height: 24),
          _FormField(
            label: 'Full Name',
            controller: _fullNameController,
            validator: (value) {
              if (value?.isEmpty ?? true) {
                return 'Please enter your full name';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          _FormField(
            label: 'Email Address',
            controller: _emailController,
            keyboardType: TextInputType.emailAddress,
            validator: (value) {
              if (value?.isEmpty ?? true) {
                return 'Please enter your email address';
              }
              if (!AppUtils.isValidEmail(value!)) {
                return 'Please enter a valid email address';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          _FormField(
            label: 'Phone Number',
            controller: _phoneController,
            keyboardType: TextInputType.phone,
            validator: (value) {
              if (value?.isEmpty ?? true) {
                return 'Please enter your phone number';
              }
              if (!AppUtils.isValidPhoneNumber(value!)) {
                return 'Please enter a valid phone number';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          _FormField(
            label: 'Date of Birth',
            controller: _dateOfBirthController,
            readOnly: true,
            onTap: () => _selectDate(context),
            validator: (value) {
              if (value?.isEmpty ?? true) {
                return 'Please select your date of birth';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          _FormField(
            label: 'National ID Number',
            controller: _nidController,
            validator: (value) {
              if (value?.isEmpty ?? true) {
                return 'Please enter your National ID number';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          _FormField(
            label: 'Address',
            controller: _addressController,
            maxLines: 3,
            validator: (value) {
              if (value?.isEmpty ?? true) {
                return 'Please enter your address';
              }
              return null;
            },
          ),
        ],
      ),
    );
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now().subtract(const Duration(days: 365 * 25)),
      firstDate: DateTime.now().subtract(const Duration(days: 365 * 100)),
      lastDate: DateTime.now().subtract(const Duration(days: 365 * 18)),
    );
    if (picked != null) {
      setState(() {
        _dateOfBirthController.text = AppUtils.formatDate(picked);
      });
    }
  }
}

/// Loan details step component
class _LoanDetailsStep extends StatefulWidget {
  final Map<String, dynamic> formData;

  const _LoanDetailsStep({required this.formData});

  @override
  State<_LoanDetailsStep> createState() => _LoanDetailsStepState();
}

class _LoanDetailsStepState extends State<_LoanDetailsStep> {
  final _amountController = TextEditingController();
  final _purposeController = TextEditingController();
  final _termController = TextEditingController();
  
  LoanType? _selectedLoanType;
  String? _selectedTerm;

  @override
  void initState() {
    super.initState();
    _loadExistingData();
  }

  void _loadExistingData() {
    _amountController.text = widget.formData['amount'] ?? '';
    _purposeController.text = widget.formData['purpose'] ?? '';
    _termController.text = widget.formData['term'] ?? '';
    _selectedLoanType = widget.formData['type'];
    _selectedTerm = widget.formData['termPeriod'];
  }

  @override
  void dispose() {
    _saveFormData();
    _amountController.dispose();
    _purposeController.dispose();
    _termController.dispose();
    super.dispose();
  }

  void _saveFormData() {
    widget.formData['amount'] = _amountController.text;
    widget.formData['purpose'] = _purposeController.text;
    widget.formData['term'] = _termController.text;
    widget.formData['type'] = _selectedLoanType;
    widget.formData['termPeriod'] = _selectedTerm;
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _StepHeader(
            title: 'Loan Details',
            subtitle: 'Specify your loan requirements',
          ),
          const SizedBox(height: 24),
          _DropdownField<LoanType>(
            label: 'Loan Type',
            value: _selectedLoanType,
            items: LoanType.values,
            itemBuilder: (type) => _getLoanTypeDisplayName(type),
            onChanged: (value) {
              setState(() {
                _selectedLoanType = value;
              });
            },
            validator: (value) {
              if (value == null) {
                return 'Please select a loan type';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          _FormField(
            label: 'Loan Amount (BDT)',
            controller: _amountController,
            keyboardType: TextInputType.number,
            inputFormatters: [FilteringTextInputFormatter.digitsOnly],
            validator: (value) {
              if (value?.isEmpty ?? true) {
                return 'Please enter the loan amount';
              }
              final amount = double.tryParse(value!);
              if (amount == null || amount <= 0) {
                return 'Please enter a valid amount';
              }
              if (amount < AppConfig.minLoanAmount) {
                return 'Minimum loan amount is ${AppUtils.formatCurrency(AppConfig.minLoanAmount)}';
              }
              if (amount > AppConfig.maxLoanAmount) {
                return 'Maximum loan amount is ${AppUtils.formatCurrency(AppConfig.maxLoanAmount)}';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          _DropdownField<String>(
            label: 'Loan Term',
            value: _selectedTerm,
            items: const ['6 months', '12 months', '24 months', '36 months', '48 months', '60 months'],
            itemBuilder: (term) => term,
            onChanged: (value) {
              setState(() {
                _selectedTerm = value;
              });
            },
            validator: (value) {
              if (value == null) {
                return 'Please select a loan term';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          _FormField(
            label: 'Purpose of Loan',
            controller: _purposeController,
            maxLines: 3,
            validator: (value) {
              if (value?.isEmpty ?? true) {
                return 'Please describe the purpose of the loan';
              }
              return null;
            },
          ),
          const SizedBox(height: 24),
          _LoanCalculator(
            amount: double.tryParse(_amountController.text) ?? 0,
            term: _selectedTerm,
          ),
        ],
      ),
    );
  }

  String _getLoanTypeDisplayName(LoanType type) {
    switch (type) {
      case LoanType.personal:
        return 'Personal Loan';
      case LoanType.business:
        return 'Business Loan';
      case LoanType.home:
        return 'Home Loan';
      case LoanType.education:
        return 'Education Loan';
      case LoanType.vehicle:
        return 'Vehicle Loan';
    }
  }
}

/// Financial information step component
class _FinancialInfoStep extends StatefulWidget {
  final Map<String, dynamic> formData;

  const _FinancialInfoStep({required this.formData});

  @override
  State<_FinancialInfoStep> createState() => _FinancialInfoStepState();
}

class _FinancialInfoStepState extends State<_FinancialInfoStep> {
  final _monthlyIncomeController = TextEditingController();
  final _employerController = TextEditingController();
  final _jobTitleController = TextEditingController();
  final _workExperienceController = TextEditingController();
  
  String? _selectedEmploymentStatus;

  @override
  void initState() {
    super.initState();
    _loadExistingData();
  }

  void _loadExistingData() {
    _monthlyIncomeController.text = widget.formData['monthlyIncome'] ?? '';
    _employerController.text = widget.formData['employer'] ?? '';
    _jobTitleController.text = widget.formData['jobTitle'] ?? '';
    _workExperienceController.text = widget.formData['workExperience'] ?? '';
    _selectedEmploymentStatus = widget.formData['employmentStatus'];
  }

  @override
  void dispose() {
    _saveFormData();
    _monthlyIncomeController.dispose();
    _employerController.dispose();
    _jobTitleController.dispose();
    _workExperienceController.dispose();
    super.dispose();
  }

  void _saveFormData() {
    widget.formData['monthlyIncome'] = _monthlyIncomeController.text;
    widget.formData['employer'] = _employerController.text;
    widget.formData['jobTitle'] = _jobTitleController.text;
    widget.formData['workExperience'] = _workExperienceController.text;
    widget.formData['employmentStatus'] = _selectedEmploymentStatus;
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _StepHeader(
            title: 'Financial Information',
            subtitle: 'Tell us about your financial situation',
          ),
          const SizedBox(height: 24),
          _DropdownField<String>(
            label: 'Employment Status',
            value: _selectedEmploymentStatus,
            items: const [
              'Full-time Employee',
              'Part-time Employee',
              'Self-employed',
              'Business Owner',
              'Freelancer',
              'Retired',
              'Student',
              'Unemployed',
            ],
            itemBuilder: (status) => status,
            onChanged: (value) {
              setState(() {
                _selectedEmploymentStatus = value;
              });
            },
            validator: (value) {
              if (value == null) {
                return 'Please select your employment status';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          _FormField(
            label: 'Monthly Income (BDT)',
            controller: _monthlyIncomeController,
            keyboardType: TextInputType.number,
            inputFormatters: [FilteringTextInputFormatter.digitsOnly],
            validator: (value) {
              if (value?.isEmpty ?? true) {
                return 'Please enter your monthly income';
              }
              final income = double.tryParse(value!);
              if (income == null || income <= 0) {
                return 'Please enter a valid income amount';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          _FormField(
            label: 'Employer/Company Name',
            controller: _employerController,
            validator: (value) {
              if (_selectedEmploymentStatus != 'Unemployed' && (value?.isEmpty ?? true)) {
                return 'Please enter your employer name';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          _FormField(
            label: 'Job Title/Position',
            controller: _jobTitleController,
            validator: (value) {
              if (_selectedEmploymentStatus != 'Unemployed' && (value?.isEmpty ?? true)) {
                return 'Please enter your job title';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          _FormField(
            label: 'Work Experience (Years)',
            controller: _workExperienceController,
            keyboardType: TextInputType.number,
            inputFormatters: [FilteringTextInputFormatter.digitsOnly],
            validator: (value) {
              if (_selectedEmploymentStatus != 'Unemployed' && (value?.isEmpty ?? true)) {
                return 'Please enter your work experience';
              }
              return null;
            },
          ),
        ],
      ),
    );
  }
}

/// Documents step component
class _DocumentsStep extends StatefulWidget {
  final Map<String, dynamic> formData;

  const _DocumentsStep({required this.formData});

  @override
  State<_DocumentsStep> createState() => _DocumentsStepState();
}

class _DocumentsStepState extends State<_DocumentsStep> {
  final List<String> _uploadedDocuments = [];

  @override
  void initState() {
    super.initState();
    _loadExistingData();
  }

  void _loadExistingData() {
    final docs = widget.formData['uploadedFiles'] as List<String>?;
    if (docs != null) {
      _uploadedDocuments.addAll(docs);
    }
  }

  @override
  void dispose() {
    _saveFormData();
    super.dispose();
  }

  void _saveFormData() {
    widget.formData['uploadedFiles'] = _uploadedDocuments;
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _StepHeader(
            title: 'Required Documents',
            subtitle: 'Upload the necessary documents',
          ),
          const SizedBox(height: 24),
          _DocumentUploadSection(
            title: 'Identity Documents',
            documents: [
              'National ID Card (Front & Back)',
              'Passport (if available)',
            ],
            uploadedDocuments: _uploadedDocuments,
            onDocumentUploaded: _onDocumentUploaded,
          ),
          const SizedBox(height: 24),
          _DocumentUploadSection(
            title: 'Income Documents',
            documents: [
              'Salary Certificate',
              'Bank Statements (Last 3 months)',
              'Tax Returns (if applicable)',
            ],
            uploadedDocuments: _uploadedDocuments,
            onDocumentUploaded: _onDocumentUploaded,
          ),
          const SizedBox(height: 24),
          _DocumentUploadSection(
            title: 'Additional Documents',
            documents: [
              'Utility Bills (Address Proof)',
              'Business License (if applicable)',
            ],
            uploadedDocuments: _uploadedDocuments,
            onDocumentUploaded: _onDocumentUploaded,
          ),
        ],
      ),
    );
  }

  void _onDocumentUploaded(String documentName) {
    setState(() {
      if (!_uploadedDocuments.contains(documentName)) {
        _uploadedDocuments.add(documentName);
      }
    });
  }
}

/// Step header component
class _StepHeader extends StatelessWidget {
  final String title;
  final String subtitle;

  const _StepHeader({
    required this.title,
    required this.subtitle,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          subtitle,
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            color: AppColors.textPrimary.withOpacity(0.7),
          ),
        ),
      ],
    );
  }
}

/// Form field component
class _FormField extends StatelessWidget {
  final String label;
  final TextEditingController controller;
  final String? Function(String?)? validator;
  final TextInputType? keyboardType;
  final List<TextInputFormatter>? inputFormatters;
  final int maxLines;
  final bool readOnly;
  final VoidCallback? onTap;

  const _FormField({
    required this.label,
    required this.controller,
    this.validator,
    this.keyboardType,
    this.inputFormatters,
    this.maxLines = 1,
    this.readOnly = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          validator: validator,
          keyboardType: keyboardType,
          inputFormatters: inputFormatters,
          maxLines: maxLines,
          readOnly: readOnly,
          onTap: onTap,
          decoration: InputDecoration(
            filled: true,
            fillColor: Colors.grey.shade50,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.grey.shade300),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.grey.shade300),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: AppColors.primary, width: 2),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Colors.red, width: 2),
            ),
            contentPadding: const EdgeInsets.all(16),
          ),
        ),
      ],
    );
  }
}

/// Dropdown field component
class _DropdownField<T> extends StatelessWidget {
  final String label;
  final T? value;
  final List<T> items;
  final String Function(T) itemBuilder;
  final void Function(T?) onChanged;
  final String? Function(T?)? validator;

  const _DropdownField({
    required this.label,
    required this.value,
    required this.items,
    required this.itemBuilder,
    required this.onChanged,
    this.validator,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<T>(
          value: value,
          items: items.map((item) {
            return DropdownMenuItem<T>(
              value: item,
              child: Text(itemBuilder(item)),
            );
          }).toList(),
          onChanged: onChanged,
          validator: validator,
          decoration: InputDecoration(
            filled: true,
            fillColor: Colors.grey.shade50,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.grey.shade300),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.grey.shade300),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: AppColors.primary, width: 2),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Colors.red, width: 2),
            ),
            contentPadding: const EdgeInsets.all(16),
          ),
        ),
      ],
    );
  }
}

/// Primary button component
class _PrimaryButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final bool isLoading;

  const _PrimaryButton({
    required this.text,
    required this.onPressed,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: isLoading ? null : onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(vertical: 16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        elevation: 0,
      ),
      child: isLoading
          ? const SizedBox(
              height: 20,
              width: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            )
          : Text(
              text,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
    );
  }
}

/// Secondary button component
class _SecondaryButton extends StatelessWidget {
  final String text;
  final VoidCallback onPressed;

  const _SecondaryButton({
    required this.text,
    required this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return OutlinedButton(
      onPressed: onPressed,
      style: OutlinedButton.styleFrom(
        foregroundColor: AppColors.primary,
        padding: const EdgeInsets.symmetric(vertical: 16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        side: const BorderSide(color: AppColors.primary),
      ),
      child: Text(
        text,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }
}

/// Loan calculator component
class _LoanCalculator extends StatelessWidget {
  final double amount;
  final String? term;

  const _LoanCalculator({
    required this.amount,
    required this.term,
  });

  @override
  Widget build(BuildContext context) {
    if (amount <= 0 || term == null) {
      return const SizedBox.shrink();
    }

    final monthlyPayment = _calculateMonthlyPayment();
    final totalPayment = _calculateTotalPayment();
    final totalInterest = totalPayment - amount;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.primary.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.primary.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Loan Calculation',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: AppColors.primary,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          _CalculationRow('Monthly Payment', AppUtils.formatCurrency(monthlyPayment)),
          _CalculationRow('Total Payment', AppUtils.formatCurrency(totalPayment)),
          _CalculationRow('Total Interest', AppUtils.formatCurrency(totalInterest)),
        ],
      ),
    );
  }

  double _calculateMonthlyPayment() {
    final months = _getMonthsFromTerm();
    const annualRate = 0.12; // 12% annual interest rate
    final monthlyRate = annualRate / 12;
    
    if (monthlyRate == 0) return amount / months;
    
    return amount * (monthlyRate * pow(1 + monthlyRate, months)) / (pow(1 + monthlyRate, months) - 1);
  }

  double _calculateTotalPayment() {
    return _calculateMonthlyPayment() * _getMonthsFromTerm();
  }

  int _getMonthsFromTerm() {
    if (term == null) return 12;
    return int.tryParse(term!.split(' ').first) ?? 12;
  }
}

/// Calculation row component
class _CalculationRow extends StatelessWidget {
  final String label;
  final String value;

  const _CalculationRow(this.label, this.value);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.textPrimary.withOpacity(0.7),
            ),
          ),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.textPrimary,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }
}

/// Document upload section component
class _DocumentUploadSection extends StatelessWidget {
  final String title;
  final List<String> documents;
  final List<String> uploadedDocuments;
  final Function(String) onDocumentUploaded;

  const _DocumentUploadSection({
    required this.title,
    required this.documents,
    required this.uploadedDocuments,
    required this.onDocumentUploaded,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        ...documents.map((document) {
          final isUploaded = uploadedDocuments.contains(document);
          return Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: _DocumentUploadCard(
              documentName: document,
              isUploaded: isUploaded,
              onUpload: () => onDocumentUploaded(document),
            ),
          );
        }).toList(),
      ],
    );
  }
}

/// Document upload card component
class _DocumentUploadCard extends StatelessWidget {
  final String documentName;
  final bool isUploaded;
  final VoidCallback onUpload;

  const _DocumentUploadCard({
    required this.documentName,
    required this.isUploaded,
    required this.onUpload,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isUploaded ? AppColors.approved.withOpacity(0.1) : Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isUploaded ? AppColors.approved : Colors.grey.shade300,
        ),
      ),
      child: Row(
        children: [
          Icon(
            isUploaded ? Icons.check_circle : Icons.upload_file,
            color: isUploaded ? AppColors.approved : Colors.grey.shade600,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              documentName,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.textPrimary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          if (!isUploaded)
            TextButton(
              onPressed: onUpload,
              child: const Text('Upload'),
            )
          else
            const Icon(
              Icons.check,
              color: AppColors.approved,
            ),
        ],
      ),
    );
  }
}

// Import for pow function
import 'dart:math';