import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/services.dart';
import 'package:image/image.dart' as img;

/// Service for OCR (Optical Character Recognition) operations
/// Handles NID scanning and text extraction from images
class OCRService {
  static final OCRService _instance = OCRService._internal();
  factory OCRService() => _instance;
  OCRService._internal();

  /// Extract information from NID image
  Future<NIDScanResult> scanNID(String imagePath) async {
    try {
      // Preprocess the image for better OCR accuracy
      final processedImage = await _preprocessImage(imagePath);
      
      // Simulate OCR processing (replace with actual OCR implementation)
      await Future.delayed(const Duration(seconds: 2));
      
      // For demo purposes, return mock data
      // In production, integrate with actual OCR service like:
      // - Google ML Kit Text Recognition
      // - Firebase ML Vision
      // - Tesseract OCR
      // - Custom trained models
      
      return _simulateNIDExtraction();
    } catch (e) {
      throw OCRException('Failed to scan NID: $e');
    }
  }

  /// Extract text from any image
  Future<String> extractText(String imagePath) async {
    try {
      // Preprocess the image
      final processedImage = await _preprocessImage(imagePath);
      
      // Simulate text extraction
      await Future.delayed(const Duration(seconds: 1));
      
      // Return simulated extracted text
      return 'Sample extracted text from image';
    } catch (e) {
      throw OCRException('Failed to extract text: $e');
    }
  }

  /// Preprocess image for better OCR accuracy
  Future<Uint8List> _preprocessImage(String imagePath) async {
    try {
      final file = File(imagePath);
      final bytes = await file.readAsBytes();
      
      // Decode the image
      final image = img.decodeImage(bytes);
      if (image == null) {
        throw const OCRException('Invalid image format');
      }

      // Apply image preprocessing techniques
      var processedImage = image;
      
      // 1. Resize if too large (max 1920x1080)
      if (image.width > 1920 || image.height > 1080) {
        processedImage = img.copyResize(
          processedImage,
          width: image.width > image.height ? 1920 : null,
          height: image.height > image.width ? 1080 : null,
        );
      }
      
      // 2. Convert to grayscale for better text recognition
      processedImage = img.grayscale(processedImage);
      
      // 3. Enhance contrast
      processedImage = img.contrast(processedImage, contrast: 1.2);
      
      // 4. Apply brightness adjustment
      processedImage = img.adjustColor(processedImage, brightness: 0.1);
      
      // 5. Apply contrast enhancement
      processedImage = img.adjustColor(processedImage, contrast: 1.2);
      
      // Encode back to bytes
      return Uint8List.fromList(img.encodePng(processedImage));
    } catch (e) {
      throw OCRException('Image preprocessing failed: $e');
    }
  }

  /// Simulate NID information extraction
  /// Replace this with actual OCR implementation
  NIDScanResult _simulateNIDExtraction() {
    // Simulate different types of NID data
    final mockData = [
      NIDScanResult(
        name: 'আব্দুল করিম',
        nameEnglish: 'Abdul Karim',
        nidNumber: '1234567890',
        dateOfBirth: DateTime(1985, 6, 15),
        fatherName: 'মোহাম্মদ রহিম',
        motherName: 'ফাতেমা খাতুন',
        address: 'গ্রাম: কামালপুর, ডাকঘর: কামালপুর, উপজেলা: সাভার, জেলা: ঢাকা',
        bloodGroup: 'B+',
        confidence: 0.92,
      ),
      NIDScanResult(
        name: 'রাহেলা বেগম',
        nameEnglish: 'Rahela Begum',
        nidNumber: '9876543210123',
        dateOfBirth: DateTime(1990, 3, 22),
        fatherName: 'আলী আহমেদ',
        motherName: 'সালমা খাতুন',
        address: 'বাড়ি: ১২৩, রোড: ৫, ব্লক: এ, মিরপুর-১০, ঢাকা-১২১৬',
        bloodGroup: 'A+',
        confidence: 0.88,
      ),
      NIDScanResult(
        name: 'মোহাম্মদ হাসান',
        nameEnglish: 'Mohammad Hasan',
        nidNumber: '19851234567890123',
        dateOfBirth: DateTime(1985, 12, 8),
        fatherName: 'আব্দুর রহমান',
        motherName: 'রোকেয়া বেগম',
        address: 'হোল্ডিং: ৪৫৬, ওয়ার্ড: ০৩, থানা: উত্তরা, ঢাকা',
        bloodGroup: 'O+',
        confidence: 0.95,
      ),
    ];
    
    // Return random mock data
    return mockData[DateTime.now().millisecond % mockData.length];
  }

  /// Validate NID number format
  bool validateNIDFormat(String nidNumber) {
    // Bangladesh NID formats:
    // - 10 digits (old format)
    // - 13 digits (new format)
    // - 17 digits (smart card format)
    final validFormats = [10, 13, 17];
    return validFormats.contains(nidNumber.length) && 
           RegExp(r'^\d+$').hasMatch(nidNumber);
  }

  /// Extract date from NID number (for new format)
  DateTime? extractDateFromNID(String nidNumber) {
    if (nidNumber.length == 17) {
      try {
        // First 4 digits represent birth year
        final year = int.parse(nidNumber.substring(0, 4));
        // Next 3 digits represent day of year
        final dayOfYear = int.parse(nidNumber.substring(4, 7));
        
        if (year >= 1900 && year <= DateTime.now().year && 
            dayOfYear >= 1 && dayOfYear <= 366) {
          final date = DateTime(year, 1, 1).add(Duration(days: dayOfYear - 1));
          return date;
        }
      } catch (e) {
        // Invalid format
      }
    }
    return null;
  }

  /// Clean and format extracted text
  String cleanExtractedText(String text) {
    return text
        .replaceAll(RegExp(r'[^\u0980-\u09FF\u0020-\u007E]'), '') // Keep Bangla and English
        .replaceAll(RegExp(r'\s+'), ' ') // Replace multiple spaces with single
        .trim();
  }

  /// Extract phone numbers from text
  List<String> extractPhoneNumbers(String text) {
    final phoneRegex = RegExp(r'(\+88)?01[3-9]\d{8}');
    return phoneRegex.allMatches(text)
        .map((match) => match.group(0)!)
        .toList();
  }

  /// Extract email addresses from text
  List<String> extractEmails(String text) {
    final emailRegex = RegExp(r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}');
    return emailRegex.allMatches(text)
        .map((match) => match.group(0)!)
        .toList();
  }

  /// Extract dates from text
  List<DateTime> extractDates(String text) {
    final dates = <DateTime>[];
    
    // DD/MM/YYYY format
    final dateRegex1 = RegExp(r'(\d{1,2})/(\d{1,2})/(\d{4})');
    for (final match in dateRegex1.allMatches(text)) {
      try {
        final day = int.parse(match.group(1)!);
        final month = int.parse(match.group(2)!);
        final year = int.parse(match.group(3)!);
        dates.add(DateTime(year, month, day));
      } catch (e) {
        // Invalid date
      }
    }
    
    // DD-MM-YYYY format
    final dateRegex2 = RegExp(r'(\d{1,2})-(\d{1,2})-(\d{4})');
    for (final match in dateRegex2.allMatches(text)) {
      try {
        final day = int.parse(match.group(1)!);
        final month = int.parse(match.group(2)!);
        final year = int.parse(match.group(3)!);
        dates.add(DateTime(year, month, day));
      } catch (e) {
        // Invalid date
      }
    }
    
    return dates;
  }

  /// Get OCR confidence level description
  String getConfidenceDescription(double confidence) {
    if (confidence >= 0.9) {
      return 'Excellent';
    } else if (confidence >= 0.8) {
      return 'Good';
    } else if (confidence >= 0.7) {
      return 'Fair';
    } else if (confidence >= 0.6) {
      return 'Poor';
    } else {
      return 'Very Poor';
    }
  }

  /// Check if OCR result needs manual verification
  bool needsManualVerification(NIDScanResult result) {
    return result.confidence < 0.8 || 
           result.name.isEmpty || 
           result.nidNumber.isEmpty ||
           !validateNIDFormat(result.nidNumber);
  }
}

/// Result of NID scanning operation
class NIDScanResult {
  final String name;
  final String nameEnglish;
  final String nidNumber;
  final DateTime? dateOfBirth;
  final String fatherName;
  final String motherName;
  final String address;
  final String bloodGroup;
  final double confidence;
  final Map<String, dynamic> rawData;

  const NIDScanResult({
    required this.name,
    required this.nameEnglish,
    required this.nidNumber,
    this.dateOfBirth,
    required this.fatherName,
    required this.motherName,
    required this.address,
    required this.bloodGroup,
    required this.confidence,
    this.rawData = const {},
  });

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'name_english': nameEnglish,
      'nid_number': nidNumber,
      'date_of_birth': dateOfBirth?.toIso8601String(),
      'father_name': fatherName,
      'mother_name': motherName,
      'address': address,
      'blood_group': bloodGroup,
      'confidence': confidence,
      'raw_data': rawData,
    };
  }

  /// Create from JSON
  factory NIDScanResult.fromJson(Map<String, dynamic> json) {
    return NIDScanResult(
      name: json['name'] ?? '',
      nameEnglish: json['name_english'] ?? '',
      nidNumber: json['nid_number'] ?? '',
      dateOfBirth: json['date_of_birth'] != null 
          ? DateTime.parse(json['date_of_birth'])
          : null,
      fatherName: json['father_name'] ?? '',
      motherName: json['mother_name'] ?? '',
      address: json['address'] ?? '',
      bloodGroup: json['blood_group'] ?? '',
      confidence: (json['confidence'] ?? 0.0).toDouble(),
      rawData: json['raw_data'] ?? {},
    );
  }

  /// Get age from date of birth
  int? get age {
    if (dateOfBirth == null) return null;
    final now = DateTime.now();
    int age = now.year - dateOfBirth!.year;
    if (now.month < dateOfBirth!.month || 
        (now.month == dateOfBirth!.month && now.day < dateOfBirth!.day)) {
      age--;
    }
    return age;
  }

  /// Check if the scan result is reliable
  bool get isReliable => confidence >= 0.8 && 
                        name.isNotEmpty && 
                        nidNumber.isNotEmpty;

  /// Get formatted confidence percentage
  String get confidencePercentage => '${(confidence * 100).toStringAsFixed(1)}%';

  /// Copy with new values
  NIDScanResult copyWith({
    String? name,
    String? nameEnglish,
    String? nidNumber,
    DateTime? dateOfBirth,
    String? fatherName,
    String? motherName,
    String? address,
    String? bloodGroup,
    double? confidence,
    Map<String, dynamic>? rawData,
  }) {
    return NIDScanResult(
      name: name ?? this.name,
      nameEnglish: nameEnglish ?? this.nameEnglish,
      nidNumber: nidNumber ?? this.nidNumber,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      fatherName: fatherName ?? this.fatherName,
      motherName: motherName ?? this.motherName,
      address: address ?? this.address,
      bloodGroup: bloodGroup ?? this.bloodGroup,
      confidence: confidence ?? this.confidence,
      rawData: rawData ?? this.rawData,
    );
  }
}

/// Custom exception for OCR operations
class OCRException implements Exception {
  final String message;
  final String? code;

  const OCRException(this.message, [this.code]);

  @override
  String toString() => 'OCRException: $message';
}

/// OCR processing status
enum OCRStatus {
  idle,
  processing,
  completed,
  failed,
}

/// OCR configuration options
class OCRConfig {
  final double minConfidence;
  final bool preprocessImage;
  final bool extractDates;
  final bool extractPhones;
  final bool extractEmails;
  final List<String> supportedLanguages;

  const OCRConfig({
    this.minConfidence = 0.7,
    this.preprocessImage = true,
    this.extractDates = true,
    this.extractPhones = true,
    this.extractEmails = true,
    this.supportedLanguages = const ['en', 'bn'],
  });
}