import '../models/loan_application.dart';

/// Statistics model for dashboard analytics
class ApplicationStatistics {
  final int totalApplications;
  final int pendingApplications;
  final int approvedApplications;
  final int rejectedApplications;
  final int disbursedApplications;
  final double totalDisbursedAmount;
  final double averageProcessingTime;
  final double approvalRate;

  ApplicationStatistics({
    required this.totalApplications,
    required this.pendingApplications,
    required this.approvedApplications,
    required this.rejectedApplications,
    required this.disbursedApplications,
    required this.totalDisbursedAmount,
    required this.averageProcessingTime,
    required this.approvalRate,
  });

  factory ApplicationStatistics.fromJson(Map<String, dynamic> json) {
    return ApplicationStatistics(
      totalApplications: json['total_applications'] ?? 0,
      pendingApplications: json['pending_applications'] ?? 0,
      approvedApplications: json['approved_applications'] ?? 0,
      rejectedApplications: json['rejected_applications'] ?? 0,
      disbursedApplications: json['disbursed_applications'] ?? 0,
      totalDisbursedAmount: (json['total_disbursed_amount'] ?? 0.0).toDouble(),
      averageProcessingTime: (json['average_processing_time'] ?? 0.0).toDouble(),
      approvalRate: (json['approval_rate'] ?? 0.0).toDouble(),
    );
  }
}

/// Abstract base class for loan application services
abstract class BaseLoanService {
  /// Initialize the service
  Future<void> initialize() async {}

  /// Fetch all loan applications with optional filtering
  Future<List<LoanApplication>> fetchApplications({
    LoanStatus? status,
    String? portfolioOfficerId,
    DateTime? fromDate,
    DateTime? toDate,
    int page = 1,
    int limit = 20,
  });

  /// Fetch a single loan application by ID
  Future<LoanApplication> fetchApplicationById(String id);

  /// Create a new loan application
  Future<LoanApplication> createApplication(LoanApplication application);

  /// Update an existing loan application
  Future<LoanApplication> updateApplication(LoanApplication application);

  /// Approve a loan application
  Future<LoanApplication> approveApplication(
    String applicationId,
    String approverId,
    String? comments,
  );

  /// Reject a loan application
  Future<LoanApplication> rejectApplication(
    String applicationId,
    String reason,
  );

  /// Disburse a loan
  Future<LoanApplication> disburseLoan(
    String applicationId,
    double amount,
    String disbursementMethod,
  );

  /// Get application statistics
  Future<ApplicationStatistics> getApplicationStatistics();

  /// Search applications
  Future<List<LoanApplication>> searchApplications(String query);

  /// Get portfolio officers
  Future<List<PortfolioOfficer>> getPortfolioOfficers();

  /// Upload document
  Future<String> uploadDocument(
    String applicationId,
    String documentType,
    String filePath,
  );
}