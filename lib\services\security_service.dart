import 'dart:convert';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:crypto/crypto.dart';

/// Comprehensive security service for authentication, encryption, and data protection
class SecurityService {
  static final SecurityService _instance = SecurityService._internal();
  factory SecurityService() => _instance;
  SecurityService._internal();

  bool _isInitialized = false;
  String? _currentUserId;
  String? _accessToken;
  String? _refreshToken;
  DateTime? _tokenExpiry;
  String? _deviceId;
  String? _encryptionKey;
  final List<SecurityEvent> _securityEvents = [];

  // Security configuration
  static const int _maxLoginAttempts = 5;
  static const Duration _lockoutDuration = Duration(minutes: 15);
  static const Duration _tokenRefreshThreshold = Duration(minutes: 5);
  static const int _passwordMinLength = 8;
  static const int _pinLength = 6;

  final Map<String, int> _loginAttempts = {};
  final Map<String, DateTime> _lockoutTimes = {};

  /// Initialize security service
  Future<void> initialize({
    String? deviceId,
    bool enableBiometrics = true,
    bool enableEncryption = true,
  }) async {
    if (_isInitialized) return;

    try {
      _deviceId = deviceId ?? await _generateDeviceId();

      if (enableEncryption) {
        await _initializeEncryption();
      }

      if (enableBiometrics) {
        await _initializeBiometrics();
      }

      // Initialize secure storage
      await _initializeSecureStorage();

      // Load stored tokens
      await _loadStoredTokens();

      // Setup security monitoring
      _setupSecurityMonitoring();

      _isInitialized = true;
      _logSecurityEvent(
          'security_service_initialized', {'device_id': _deviceId});
    } catch (e) {
      throw SecurityException('Failed to initialize security service: $e');
    }
  }

  /// Initialize encryption
  Future<void> _initializeEncryption() async {
    try {
      // In production, use flutter_secure_storage or similar
      // to store encryption keys securely
      _encryptionKey = await _getOrCreateEncryptionKey();
    } catch (e) {
      throw SecurityException('Failed to initialize encryption: $e');
    }
  }

  /// Initialize biometrics
  Future<void> _initializeBiometrics() async {
    try {
      // In production, use local_auth package:
      // final localAuth = LocalAuthentication();
      // final isAvailable = await localAuth.canCheckBiometrics;
      // final isDeviceSupported = await localAuth.isDeviceSupported();

      print('Biometrics initialized');
    } catch (e) {
      print('Failed to initialize biometrics: $e');
    }
  }

  /// Initialize secure storage
  Future<void> _initializeSecureStorage() async {
    try {
      // In production, use flutter_secure_storage:
      // const storage = FlutterSecureStorage(
      //   aOptions: AndroidOptions(
      //     encryptedSharedPreferences: true,
      //   ),
      //   iOptions: IOSOptions(
      //     accessibility: IOSAccessibility.first_unlock_this_device,
      //   ),
      // );

      print('Secure storage initialized');
    } catch (e) {
      throw SecurityException('Failed to initialize secure storage: $e');
    }
  }

  /// Load stored authentication tokens
  Future<void> _loadStoredTokens() async {
    try {
      // In production, load from secure storage:
      // _accessToken = await storage.read(key: 'access_token');
      // _refreshToken = await storage.read(key: 'refresh_token');
      // final expiryString = await storage.read(key: 'token_expiry');
      // if (expiryString != null) {
      //   _tokenExpiry = DateTime.parse(expiryString);
      // }

      print('Stored tokens loaded');
    } catch (e) {
      print('Failed to load stored tokens: $e');
    }
  }

  /// Setup security monitoring
  void _setupSecurityMonitoring() {
    // Monitor for security events and anomalies
    // This could include unusual login patterns, multiple failed attempts, etc.
  }

  /// Authenticate user with email and password
  Future<AuthenticationResult> authenticateWithPassword(
    String email,
    String password,
  ) async {
    try {
      // Check if user is locked out
      if (_isUserLockedOut(email)) {
        final lockoutTime = _lockoutTimes[email]!;
        final remainingTime =
            lockoutTime.add(_lockoutDuration).difference(DateTime.now());

        _logSecurityEvent('authentication_blocked_lockout', {
          'email': _hashEmail(email),
          'remaining_lockout_seconds': remainingTime.inSeconds,
        });

        return AuthenticationResult(
          success: false,
          errorCode: 'ACCOUNT_LOCKED',
          errorMessage:
              'Account locked. Try again in ${remainingTime.inMinutes} minutes.',
        );
      }

      // Validate password strength
      final passwordValidation = validatePassword(password);
      if (!passwordValidation.isValid) {
        return AuthenticationResult(
          success: false,
          errorCode: 'WEAK_PASSWORD',
          errorMessage: passwordValidation.message,
        );
      }

      // Simulate authentication API call
      final authResult = await _performAuthentication(email, password);

      if (authResult.success) {
        // Reset login attempts on successful authentication
        _loginAttempts.remove(email);
        _lockoutTimes.remove(email);

        // Store tokens securely
        await _storeTokens(authResult.accessToken!, authResult.refreshToken!);

        _currentUserId = authResult.userId;
        _accessToken = authResult.accessToken;
        _refreshToken = authResult.refreshToken;
        _tokenExpiry = authResult.tokenExpiry;

        _logSecurityEvent('authentication_success', {
          'user_id': authResult.userId,
          'method': 'password',
        });
      } else {
        // Increment failed login attempts
        _loginAttempts[email] = (_loginAttempts[email] ?? 0) + 1;

        // Lock account if max attempts reached
        if (_loginAttempts[email]! >= _maxLoginAttempts) {
          _lockoutTimes[email] = DateTime.now();

          _logSecurityEvent('account_locked', {
            'email': _hashEmail(email),
            'failed_attempts': _loginAttempts[email],
          });
        } else {
          _logSecurityEvent('authentication_failed', {
            'email': _hashEmail(email),
            'attempt_count': _loginAttempts[email],
            'remaining_attempts': _maxLoginAttempts - _loginAttempts[email]!,
          });
        }
      }

      return authResult;
    } catch (e) {
      _logSecurityEvent('authentication_error', {
        'email': _hashEmail(email),
        'error': e.toString(),
      });

      return const AuthenticationResult(
        success: false,
        errorCode: 'AUTHENTICATION_ERROR',
        errorMessage: 'Authentication failed. Please try again.',
      );
    }
  }

  /// Authenticate with biometrics
  Future<AuthenticationResult> authenticateWithBiometrics() async {
    try {
      // In production, use local_auth package:
      // final localAuth = LocalAuthentication();
      // final isAuthenticated = await localAuth.authenticate(
      //   localizedReason: 'Please authenticate to access your account',
      //   options: AuthenticationOptions(
      //     biometricOnly: true,
      //     stickyAuth: true,
      //   ),
      // );

      // For demo purposes, simulate successful biometric authentication
      const isAuthenticated = true;

      if (isAuthenticated) {
        // Load stored tokens
        await _loadStoredTokens();

        if (_accessToken != null && _isTokenValid()) {
          _logSecurityEvent('authentication_success', {
            'user_id': _currentUserId,
            'method': 'biometric',
          });

          return AuthenticationResult(
            success: true,
            userId: _currentUserId,
            accessToken: _accessToken,
            refreshToken: _refreshToken,
            tokenExpiry: _tokenExpiry,
          );
        } else {
          return const AuthenticationResult(
            success: false,
            errorCode: 'TOKEN_EXPIRED',
            errorMessage: 'Please login with your password.',
          );
        }
      }
    } catch (e) {
      _logSecurityEvent('biometric_authentication_error', {
        'error': e.toString(),
      });

      return const AuthenticationResult(
        success: false,
        errorCode: 'BIOMETRIC_ERROR',
        errorMessage: 'Biometric authentication error.',
      );
    }
  }

  /// Authenticate with PIN
  Future<AuthenticationResult> authenticateWithPIN(String pin) async {
    try {
      if (pin.length != _pinLength) {
        return const AuthenticationResult(
          success: false,
          errorCode: 'INVALID_PIN_LENGTH',
          errorMessage: 'PIN must be $_pinLength digits.',
        );
      }

      // In production, verify PIN against stored hash
      // final storedPinHash = await storage.read(key: 'pin_hash');
      // final pinHash = _hashPin(pin);
      // final isValid = storedPinHash == pinHash;

      // For demo purposes, simulate PIN verification
      final isValid = pin == '123456'; // Demo PIN

      if (isValid) {
        await _loadStoredTokens();

        if (_accessToken != null && _isTokenValid()) {
          _logSecurityEvent('authentication_success', {
            'user_id': _currentUserId,
            'method': 'pin',
          });

          return AuthenticationResult(
            success: true,
            userId: _currentUserId,
            accessToken: _accessToken,
            refreshToken: _refreshToken,
            tokenExpiry: _tokenExpiry,
          );
        } else {
          return const AuthenticationResult(
            success: false,
            errorCode: 'TOKEN_EXPIRED',
            errorMessage: 'Please login with your password.',
          );
        }
      } else {
        _logSecurityEvent('pin_authentication_failed', {});

        return const AuthenticationResult(
          success: false,
          errorCode: 'INVALID_PIN',
          errorMessage: 'Invalid PIN.',
        );
      }
    } catch (e) {
      _logSecurityEvent('pin_authentication_error', {
        'error': e.toString(),
      });

      return const AuthenticationResult(
        success: false,
        errorCode: 'PIN_ERROR',
        errorMessage: 'PIN authentication error.',
      );
    }
  }

  /// Refresh authentication token
  Future<bool> refreshToken() async {
    if (_refreshToken == null) return false;

    try {
      // In production, call refresh token API:
      // final response = await http.post(
      //   Uri.parse('$baseUrl/auth/refresh'),
      //   headers: {'Content-Type': 'application/json'},
      //   body: jsonEncode({'refresh_token': _refreshToken}),
      // );

      // For demo purposes, simulate token refresh
      final newAccessToken =
          'new_access_token_${DateTime.now().millisecondsSinceEpoch}';
      final newRefreshToken =
          'new_refresh_token_${DateTime.now().millisecondsSinceEpoch}';
      final newExpiry = DateTime.now().add(const Duration(hours: 1));

      await _storeTokens(newAccessToken, newRefreshToken);

      _accessToken = newAccessToken;
      _refreshToken = newRefreshToken;
      _tokenExpiry = newExpiry;

      _logSecurityEvent('token_refreshed', {
        'user_id': _currentUserId,
      });

      return true;
    } catch (e) {
      _logSecurityEvent('token_refresh_failed', {
        'user_id': _currentUserId,
        'error': e.toString(),
      });

      return false;
    }
  }

  /// Logout user
  Future<void> logout() async {
    try {
      // Revoke tokens on server
      if (_accessToken != null) {
        await _revokeTokens();
      }

      // Clear stored tokens
      await _clearStoredTokens();

      // Clear session data
      _currentUserId = null;
      _accessToken = null;
      _refreshToken = null;
      _tokenExpiry = null;

      _logSecurityEvent('user_logout', {
        'user_id': _currentUserId,
      });
    } catch (e) {
      _logSecurityEvent('logout_error', {
        'error': e.toString(),
      });
    }
  }

  /// Validate password strength
  PasswordValidationResult validatePassword(String password) {
    final issues = <String>[];

    if (password.length < _passwordMinLength) {
      issues
          .add('Password must be at least $_passwordMinLength characters long');
    }

    if (!password.contains(RegExp(r'[A-Z]'))) {
      issues.add('Password must contain at least one uppercase letter');
    }

    if (!password.contains(RegExp(r'[a-z]'))) {
      issues.add('Password must contain at least one lowercase letter');
    }

    if (!password.contains(RegExp(r'[0-9]'))) {
      issues.add('Password must contain at least one number');
    }

    if (!password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'))) {
      issues.add('Password must contain at least one special character');
    }

    // Check for common passwords
    if (_isCommonPassword(password)) {
      issues
          .add('Password is too common. Please choose a more unique password');
    }

    return PasswordValidationResult(
      isValid: issues.isEmpty,
      issues: issues,
      strength: _calculatePasswordStrength(password),
    );
  }

  /// Calculate password strength
  PasswordStrength _calculatePasswordStrength(String password) {
    int score = 0;

    // Length bonus
    if (password.length >= 8) score += 1;
    if (password.length >= 12) score += 1;
    if (password.length >= 16) score += 1;

    // Character variety bonus
    if (password.contains(RegExp(r'[a-z]'))) score += 1;
    if (password.contains(RegExp(r'[A-Z]'))) score += 1;
    if (password.contains(RegExp(r'[0-9]'))) score += 1;
    if (password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'))) score += 1;

    // Complexity bonus
    if (password.contains(RegExp(r'[a-z].*[A-Z]|[A-Z].*[a-z]'))) score += 1;
    if (password.contains(RegExp(r'[a-zA-Z].*[0-9]|[0-9].*[a-zA-Z]'))) {
      score += 1;
    }

    if (score <= 3) return PasswordStrength.weak;
    if (score <= 6) return PasswordStrength.medium;
    if (score <= 8) return PasswordStrength.strong;
    return PasswordStrength.veryStrong;
  }

  /// Check if password is commonly used
  bool _isCommonPassword(String password) {
    final commonPasswords = [
      'password',
      '123456',
      '123456789',
      'qwerty',
      'abc123',
      'password123',
      'admin',
      'letmein',
      'welcome',
      'monkey',
    ];

    return commonPasswords.contains(password.toLowerCase());
  }

  /// Encrypt sensitive data
  String encryptData(String data) {
    if (_encryptionKey == null) {
      throw const SecurityException('Encryption not initialized');
    }

    try {
      // In production, use proper encryption library like pointycastle
      // For demo purposes, use simple base64 encoding
      final bytes = utf8.encode(data);
      return base64Encode(bytes);
    } catch (e) {
      throw SecurityException('Failed to encrypt data: $e');
    }
  }

  /// Decrypt sensitive data
  String decryptData(String encryptedData) {
    if (_encryptionKey == null) {
      throw const SecurityException('Encryption not initialized');
    }

    try {
      // In production, use proper decryption
      // For demo purposes, use simple base64 decoding
      final bytes = base64Decode(encryptedData);
      return utf8.decode(bytes);
    } catch (e) {
      throw SecurityException('Failed to decrypt data: $e');
    }
  }

  /// Generate secure random string
  String generateSecureRandom(int length) {
    const chars =
        'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    final random = Random.secure();
    return String.fromCharCodes(
      Iterable.generate(
          length, (_) => chars.codeUnitAt(random.nextInt(chars.length))),
    );
  }

  /// Hash sensitive data
  String hashData(String data, {String? salt}) {
    final saltToUse = salt ?? generateSecureRandom(16);
    final bytes = utf8.encode(data + saltToUse);
    final digest = sha256.convert(bytes);
    return '${digest.toString()}:$saltToUse';
  }

  /// Verify hashed data
  bool verifyHash(String data, String hashedData) {
    try {
      final parts = hashedData.split(':');
      if (parts.length != 2) return false;

      final hash = parts[0];
      final salt = parts[1];

      final newHash = hashData(data, salt: salt).split(':')[0];
      return hash == newHash;
    } catch (e) {
      return false;
    }
  }

  /// Check if user is currently authenticated
  bool get isAuthenticated {
    return _accessToken != null && _isTokenValid();
  }

  /// Check if token needs refresh
  bool get needsTokenRefresh {
    if (_tokenExpiry == null) return false;
    return DateTime.now().add(_tokenRefreshThreshold).isAfter(_tokenExpiry!);
  }

  /// Get current user ID
  String? get currentUserId => _currentUserId;

  /// Get current access token
  String? get accessToken => _accessToken;

  /// Get device ID
  String? get deviceId => _deviceId;

  /// Get security events
  List<SecurityEvent> getSecurityEvents({int? limit}) {
    final events = List<SecurityEvent>.from(_securityEvents);
    if (limit != null && limit < events.length) {
      return events.take(limit).toList();
    }
    return events;
  }

  /// Clear security events
  void clearSecurityEvents() {
    _securityEvents.clear();
  }

  /// Helper methods

  Future<AuthenticationResult> _performAuthentication(
      String email, String password) async {
    // Simulate API call delay
    await Future.delayed(const Duration(milliseconds: 500));

    // Demo authentication logic
    if (email == '<EMAIL>' && password == 'Password123!') {
      return AuthenticationResult(
        success: true,
        userId: 'user_123',
        accessToken: 'access_token_${DateTime.now().millisecondsSinceEpoch}',
        refreshToken: 'refresh_token_${DateTime.now().millisecondsSinceEpoch}',
        tokenExpiry: DateTime.now().add(const Duration(hours: 1)),
      );
    } else {
      return const AuthenticationResult(
        success: false,
        errorCode: 'INVALID_CREDENTIALS',
        errorMessage: 'Invalid email or password.',
      );
    }
  }

  bool _isUserLockedOut(String email) {
    final lockoutTime = _lockoutTimes[email];
    if (lockoutTime == null) return false;

    return DateTime.now().isBefore(lockoutTime.add(_lockoutDuration));
  }

  bool _isTokenValid() {
    if (_tokenExpiry == null) return false;
    return DateTime.now().isBefore(_tokenExpiry!);
  }

  Future<void> _storeTokens(String accessToken, String refreshToken) async {
    try {
      // In production, store in secure storage:
      // await storage.write(key: 'access_token', value: accessToken);
      // await storage.write(key: 'refresh_token', value: refreshToken);
      // await storage.write(key: 'token_expiry', value: _tokenExpiry?.toIso8601String());
    } catch (e) {
      throw SecurityException('Failed to store tokens: $e');
    }
  }

  Future<void> _clearStoredTokens() async {
    try {
      // In production, clear from secure storage:
      // await storage.delete(key: 'access_token');
      // await storage.delete(key: 'refresh_token');
      // await storage.delete(key: 'token_expiry');
    } catch (e) {
      print('Failed to clear stored tokens: $e');
    }
  }

  Future<void> _revokeTokens() async {
    try {
      // In production, call token revocation API:
      // await http.post(
      //   Uri.parse('$baseUrl/auth/revoke'),
      //   headers: {
      //     'Content-Type': 'application/json',
      //     'Authorization': 'Bearer $_accessToken',
      //   },
      //   body: jsonEncode({'refresh_token': _refreshToken}),
      // );
    } catch (e) {
      print('Failed to revoke tokens: $e');
    }
  }

  Future<String> _generateDeviceId() async {
    // In production, use device_info_plus package to get unique device identifier
    return 'device_${DateTime.now().millisecondsSinceEpoch}';
  }

  Future<String> _getOrCreateEncryptionKey() async {
    // In production, generate or retrieve encryption key from secure storage
    return generateSecureRandom(32);
  }

  String _hashEmail(String email) {
    final bytes = utf8.encode(email.toLowerCase());
    final digest = sha256.convert(bytes);
    return digest.toString().substring(0, 8); // First 8 characters for privacy
  }

  String _hashPin(String pin) {
    return hashData(pin);
  }

  void _logSecurityEvent(String eventType, Map<String, dynamic> data) {
    final event = SecurityEvent(
      type: eventType,
      timestamp: DateTime.now(),
      data: data,
      deviceId: _deviceId,
      userId: _currentUserId,
    );

    _securityEvents.insert(0, event);

    // Limit security events storage
    if (_securityEvents.length > 1000) {
      _securityEvents.removeRange(1000, _securityEvents.length);
    }

    if (kDebugMode) {
      print('🔒 Security Event: $eventType');
      if (data.isNotEmpty) {
        print('   Data: ${jsonEncode(data)}');
      }
    }
  }

  /// Dispose resources
  void dispose() {
    _securityEvents.clear();
    _loginAttempts.clear();
    _lockoutTimes.clear();
  }
}

/// Authentication result model
class AuthenticationResult {
  final bool success;
  final String? userId;
  final String? accessToken;
  final String? refreshToken;
  final DateTime? tokenExpiry;
  final String? errorCode;
  final String? errorMessage;

  const AuthenticationResult({
    required this.success,
    this.userId,
    this.accessToken,
    this.refreshToken,
    this.tokenExpiry,
    this.errorCode,
    this.errorMessage,
  });

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'user_id': userId,
      'access_token': accessToken,
      'refresh_token': refreshToken,
      'token_expiry': tokenExpiry?.toIso8601String(),
      'error_code': errorCode,
      'error_message': errorMessage,
    };
  }
}

/// Password validation result
class PasswordValidationResult {
  final bool isValid;
  final List<String> issues;
  final PasswordStrength strength;

  const PasswordValidationResult({
    required this.isValid,
    required this.issues,
    required this.strength,
  });

  String get message => issues.join('. ');
}

/// Password strength levels
enum PasswordStrength {
  weak,
  medium,
  strong,
  veryStrong,
}

/// Security event model
class SecurityEvent {
  final String type;
  final DateTime timestamp;
  final Map<String, dynamic> data;
  final String? deviceId;
  final String? userId;

  const SecurityEvent({
    required this.type,
    required this.timestamp,
    required this.data,
    this.deviceId,
    this.userId,
  });

  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'timestamp': timestamp.toIso8601String(),
      'data': data,
      'device_id': deviceId,
      'user_id': userId,
    };
  }

  factory SecurityEvent.fromJson(Map<String, dynamic> json) {
    return SecurityEvent(
      type: json['type'],
      timestamp: DateTime.parse(json['timestamp']),
      data: Map<String, dynamic>.from(json['data']),
      deviceId: json['device_id'],
      userId: json['user_id'],
    );
  }
}

/// Custom exception for security operations
class SecurityException implements Exception {
  final String message;
  final String? code;

  const SecurityException(this.message, [this.code]);

  @override
  String toString() => 'SecurityException: $message';
}
