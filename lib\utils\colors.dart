import 'package:flutter/material.dart';

class AppColors {
  // Primary: A calming teal/green (hsl(171, 38%, 45%))
  static const Color primary = Color(0xFF4A9B8E);
  
  // Background: A very light, slightly green-tinted white (hsl(170, 53%, 92%))
  static const Color background = Color(0xFFE8F5F3);
  
  // Cards: An off-white, almost pure white with a hint of the background color (hsl(170, 53%, 98%))
  static const Color cardBackground = Color(0xFFF8FCFB);
  
  // Text: A dark, muted teal for good readability (hsl(171, 25%, 25%))
  static const Color textPrimary = Color(0xFF2F4F4A);
  
  // Secondary colors for different actions
  static const Color secondary = Color(0xFFBF6D24); // Gold/Orange for submit buttons
  static const Color accent = Color(0xFF27498C); // Blue for approve buttons
  static const Color error = Color(0xFF732306); // Red for reject buttons
  
  // Status colors
  static const Color pending = Color(0xFFBF6D24);
  static const Color approved = Color(0xFF27498C);
  static const Color rejected = Color(0xFF732306);
  static const Color disbursed = Color(0xFF4A9B8E);
  
  // Gradient colors
  static const Color khmerGoldStart = Color(0xFFBF6D24);
  static const Color khmerGoldEnd = Color(0xFFD4822A);
  static const Color templeBlueStart = Color(0xFF27498C);
  static const Color templeBlueEnd = Color(0xFF3A5BA0);
  static const Color lotusPinkStart = Color(0xFFE91E63);
  static const Color lotusPinkEnd = Color(0xFFF06292);
}