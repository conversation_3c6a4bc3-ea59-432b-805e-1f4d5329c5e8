
import '../models/loan_application.dart';

/// Comprehensive validation utilities for loan application forms
class ValidationUtils {
  // Regular expressions for validation
  static final RegExp _emailRegex = RegExp(
    r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
  );

  static final RegExp _phoneRegex = RegExp(
    r'^(\+88)?01[3-9]\d{8}$', // Bangladesh phone number format
  );

  static final RegExp _nidRegex = RegExp(
    r'^\d{10}$|^\d{13}$|^\d{17}$', // Bangladesh NID formats
  );

  static final RegExp _nameRegex = RegExp(
    r'^[a-zA-Z\s.]+$', // Only letters, spaces, and dots
  );

  /// Validate email address
  static ValidationResult validateEmail(String email) {
    if (email.isEmpty) {
      return ValidationResult.error('Email is required');
    }

    if (!_emailRegex.hasMatch(email)) {
      return ValidationResult.error('Please enter a valid email address');
    }

    return ValidationResult.success();
  }

  /// Validate phone number (Bangladesh format)
  static ValidationResult validatePhoneNumber(String phone) {
    if (phone.isEmpty) {
      return ValidationResult.error('Phone number is required');
    }

    // Remove spaces and dashes
    final cleanPhone = phone.replaceAll(RegExp(r'[\s-]'), '');

    if (!_phoneRegex.hasMatch(cleanPhone)) {
      return ValidationResult.error(
        'Please enter a valid Bangladesh phone number (e.g., 01712345678)',
      );
    }

    return ValidationResult.success();
  }

  /// Validate NID number (Bangladesh format)
  static ValidationResult validateNidNumber(String nid) {
    if (nid.isEmpty) {
      return ValidationResult.error('NID number is required');
    }

    if (!_nidRegex.hasMatch(nid)) {
      return ValidationResult.error(
        'Please enter a valid NID number (10, 13, or 17 digits)',
      );
    }

    return ValidationResult.success();
  }

  /// Validate person name
  static ValidationResult validateName(String name,
      {String fieldName = 'Name'}) {
    if (name.isEmpty) {
      return ValidationResult.error('$fieldName is required');
    }

    if (name.length < 2) {
      return ValidationResult.error('$fieldName must be at least 2 characters');
    }

    if (name.length > 50) {
      return ValidationResult.error('$fieldName must not exceed 50 characters');
    }

    if (!_nameRegex.hasMatch(name)) {
      return ValidationResult.error(
        '$fieldName can only contain letters, spaces, and dots',
      );
    }

    return ValidationResult.success();
  }

  /// Validate address
  static ValidationResult validateAddress(String address) {
    if (address.isEmpty) {
      return ValidationResult.error('Address is required');
    }

    if (address.length < 10) {
      return ValidationResult.error('Address must be at least 10 characters');
    }

    if (address.length > 200) {
      return ValidationResult.error('Address must not exceed 200 characters');
    }

    return ValidationResult.success();
  }

  /// Validate date of birth
  static ValidationResult validateDateOfBirth(DateTime? dateOfBirth) {
    if (dateOfBirth == null) {
      return ValidationResult.error('Date of birth is required');
    }

    final now = DateTime.now();
    final age = now.year - dateOfBirth.year;

    if (dateOfBirth.isAfter(now)) {
      return ValidationResult.error('Date of birth cannot be in the future');
    }

    if (age < 18) {
      return ValidationResult.error('Applicant must be at least 18 years old');
    }

    if (age > 80) {
      return ValidationResult.error('Applicant must be under 80 years old');
    }

    return ValidationResult.success();
  }

  /// Validate loan amount
  static ValidationResult validateLoanAmount(
    double amount, {
    double minAmount = 5000,
    double maxAmount = 500000,
  }) {
    if (amount <= 0) {
      return ValidationResult.error('Loan amount must be greater than 0');
    }

    if (amount < minAmount) {
      return ValidationResult.error(
        'Minimum loan amount is ৳${_formatCurrency(minAmount)}',
      );
    }

    if (amount > maxAmount) {
      return ValidationResult.error(
        'Maximum loan amount is ৳${_formatCurrency(maxAmount)}',
      );
    }

    // Amount should be in multiples of 1000
    if (amount % 1000 != 0) {
      return ValidationResult.error(
        'Loan amount should be in multiples of ৳1,000',
      );
    }

    return ValidationResult.success();
  }

  /// Validate loan term
  static ValidationResult validateLoanTerm(
    int term, {
    int minTerm = 6,
    int maxTerm = 36,
  }) {
    if (term <= 0) {
      return ValidationResult.error('Loan term must be greater than 0');
    }

    if (term < minTerm) {
      return ValidationResult.error('Minimum loan term is $minTerm months');
    }

    if (term > maxTerm) {
      return ValidationResult.error('Maximum loan term is $maxTerm months');
    }

    return ValidationResult.success();
  }

  /// Validate monthly income
  static ValidationResult validateMonthlyIncome(
    double income, {
    double minIncome = 10000,
  }) {
    if (income <= 0) {
      return ValidationResult.error('Monthly income must be greater than 0');
    }

    if (income < minIncome) {
      return ValidationResult.error(
        'Minimum monthly income is ৳${_formatCurrency(minIncome)}',
      );
    }

    return ValidationResult.success();
  }

  /// Validate business years
  static ValidationResult validateBusinessYears(int years) {
    if (years < 0) {
      return ValidationResult.error('Years in business cannot be negative');
    }

    if (years > 50) {
      return ValidationResult.error('Years in business seems unrealistic');
    }

    return ValidationResult.success();
  }

  /// Validate number of children
  static ValidationResult validateNumberOfChildren(int children) {
    if (children < 0) {
      return ValidationResult.error('Number of children cannot be negative');
    }

    if (children > 20) {
      return ValidationResult.error('Number of children seems unrealistic');
    }

    return ValidationResult.success();
  }

  /// Validate asset value
  static ValidationResult validateAssetValue(double value, String assetType) {
    if (value < 0) {
      return ValidationResult.error('$assetType value cannot be negative');
    }

    return ValidationResult.success();
  }

  /// Validate debt-to-income ratio
  static ValidationResult validateDebtToIncomeRatio(
    double monthlyIncome,
    double monthlyExpenses,
    double proposedLoanPayment,
  ) {
    final totalMonthlyDebt = monthlyExpenses + proposedLoanPayment;
    final debtToIncomeRatio = totalMonthlyDebt / monthlyIncome;

    if (debtToIncomeRatio > 0.6) {
      return ValidationResult.error(
        'Debt-to-income ratio is too high (${(debtToIncomeRatio * 100).toStringAsFixed(1)}%). '
        'Maximum allowed is 60%.',
      );
    }

    if (debtToIncomeRatio > 0.5) {
      return ValidationResult.warning(
        'Debt-to-income ratio is high (${(debtToIncomeRatio * 100).toStringAsFixed(1)}%). '
        'Consider reducing the loan amount.',
      );
    }

    return ValidationResult.success();
  }

  /// Calculate loan affordability
  static LoanAffordabilityResult calculateLoanAffordability(
    double monthlyIncome,
    double monthlyExpenses,
    double requestedAmount,
    int loanTerm,
    double interestRate,
  ) {
    // Calculate monthly payment using simple interest
    final totalInterest =
        (requestedAmount * interestRate * loanTerm) / (12 * 100);
    final totalAmount = requestedAmount + totalInterest;
    final monthlyPayment = totalAmount / loanTerm;

    final availableIncome = monthlyIncome - monthlyExpenses;
    final debtToIncomeRatio = monthlyPayment / monthlyIncome;

    bool isAffordable = true;
    List<String> concerns = [];

    if (monthlyPayment > availableIncome) {
      isAffordable = false;
      concerns.add('Monthly payment exceeds available income');
    }

    if (debtToIncomeRatio > 0.6) {
      isAffordable = false;
      concerns.add('Debt-to-income ratio exceeds 60%');
    }

    if (availableIncome - monthlyPayment < 5000) {
      concerns.add('Very little income left after loan payment');
    }

    // Calculate maximum affordable loan
    final maxMonthlyPayment = monthlyIncome * 0.5; // 50% of income
    final maxAffordableAmount = (maxMonthlyPayment * loanTerm) /
        (1 + (interestRate * loanTerm) / (12 * 100));

    return LoanAffordabilityResult(
      isAffordable: isAffordable,
      monthlyPayment: monthlyPayment,
      debtToIncomeRatio: debtToIncomeRatio,
      availableIncomeAfterPayment: availableIncome - monthlyPayment,
      maxAffordableAmount: maxAffordableAmount,
      concerns: concerns,
    );
  }

  /// Validate complete loan application
  static List<ValidationResult> validateLoanApplication(
      LoanApplication application) {
    final results = <ValidationResult>[];

    // Validate applicant
    results.add(
        validateName(application.applicant.name, fieldName: 'Applicant name'));
    results.add(validatePhoneNumber(application.applicant.phoneNumber));
    results.add(validateNidNumber(application.applicant.nationalId));
    results.add(validateAddress(application.applicant.address));
    results.add(validateDateOfBirth(application.applicant.dateOfBirth));

    // Validate business
    results.add(
        validateName(application.business.name, fieldName: 'Business name'));
    results.add(validateAddress(application.business.address));
    results.add(validateMonthlyIncome(application.business.monthlyIncome));
    results.add(validateBusinessYears(application.business.yearsInOperation));

    // Validate family
    results.add(validateNumberOfChildren(application.family.numberOfChildren));
    results.add(validateAssetValue(
      application.family.familyIncome,
      'Monthly household income',
    ));

    // Validate assets
    results
        .add(validateAssetValue(application.assets.estimatedValue, 'Assets'));
    results
        .add(validateAssetValue(application.assets.totalValue, 'Total assets'));

    // Validate guarantor
    results.add(
        validateName(application.guarantor.name, fieldName: 'Guarantor name'));
    results.add(validatePhoneNumber(application.guarantor.phoneNumber));
    results.add(validateNidNumber(application.guarantor.nid));
    results.add(validateAddress(application.guarantor.address));
    results.add(validateMonthlyIncome(application.guarantor.monthlyIncome));

    // Validate loan details
    results.add(validateLoanAmount(application.requestedAmount));
    results.add(validateLoanTerm(application.loanTerm));

    // Validate debt-to-income ratio
    final monthlyPayment = _calculateMonthlyPayment(
      application.requestedAmount,
      application.loanTerm,
      12.0, // Assumed interest rate
    );

    results.add(validateDebtToIncomeRatio(
      application.business.monthlyIncome,
      application.family.familyIncome,
      monthlyPayment,
    ));

    return results;
  }

  /// Helper method to calculate monthly payment
  static double _calculateMonthlyPayment(
    double principal,
    int termInMonths,
    double annualInterestRate,
  ) {
    final monthlyRate = annualInterestRate / (12 * 100);
    final totalInterest = principal * monthlyRate * termInMonths;
    return (principal + totalInterest) / termInMonths;
  }

  /// Helper method to format currency
  static String _formatCurrency(double amount) {
    return amount.toStringAsFixed(0).replaceAllMapped(
          RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
          (Match m) => '${m[1]},',
        );
  }
}

/// Result of a validation operation
class ValidationResult {
  final bool isValid;
  final String? message;
  final ValidationSeverity severity;

  const ValidationResult._(
    this.isValid,
    this.message,
    this.severity,
  );

  factory ValidationResult.success() {
    return const ValidationResult._(true, null, ValidationSeverity.none);
  }

  factory ValidationResult.error(String message) {
    return ValidationResult._(false, message, ValidationSeverity.error);
  }

  factory ValidationResult.warning(String message) {
    return ValidationResult._(true, message, ValidationSeverity.warning);
  }

  factory ValidationResult.info(String message) {
    return ValidationResult._(true, message, ValidationSeverity.info);
  }
}

/// Severity levels for validation messages
enum ValidationSeverity {
  none,
  info,
  warning,
  error,
}

/// Result of loan affordability calculation
class LoanAffordabilityResult {
  final bool isAffordable;
  final double monthlyPayment;
  final double debtToIncomeRatio;
  final double availableIncomeAfterPayment;
  final double maxAffordableAmount;
  final List<String> concerns;

  const LoanAffordabilityResult({
    required this.isAffordable,
    required this.monthlyPayment,
    required this.debtToIncomeRatio,
    required this.availableIncomeAfterPayment,
    required this.maxAffordableAmount,
    required this.concerns,
  });

  /// Get affordability status as a string
  String get statusText {
    if (isAffordable && concerns.isEmpty) {
      return 'Excellent affordability';
    } else if (isAffordable && concerns.isNotEmpty) {
      return 'Affordable with concerns';
    } else {
      return 'Not affordable';
    }
  }

  /// Get recommendation text
  String get recommendation {
    if (isAffordable && concerns.isEmpty) {
      return 'This loan amount is well within your financial capacity.';
    } else if (isAffordable && concerns.isNotEmpty) {
      return 'Consider reducing the loan amount for better financial comfort.';
    } else {
      return 'Please consider a smaller loan amount of ৳${ValidationUtils._formatCurrency(maxAffordableAmount)}.';
    }
  }
}

/// Extension methods for validation
extension ValidationListExtension on List<ValidationResult> {
  /// Check if all validations passed
  bool get allValid => every((result) => result.isValid);

  /// Get all error messages
  List<String> get errorMessages =>
      where((result) => result.severity == ValidationSeverity.error)
          .map((result) => result.message!)
          .toList();

  /// Get all warning messages
  List<String> get warningMessages =>
      where((result) => result.severity == ValidationSeverity.warning)
          .map((result) => result.message!)
          .toList();

  /// Get first error message
  String? get firstError => firstWhere(
        (result) => result.severity == ValidationSeverity.error,
        orElse: () => ValidationResult.success(),
      ).message;
}
