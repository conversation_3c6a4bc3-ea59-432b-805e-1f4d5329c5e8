# Mock Data Implementation for Cambodian Loan Application

## Overview

This implementation provides comprehensive mock data for the Cambodian loan application system, designed to showcase the application workflow and user interface without requiring a backend API.

## Files Created

### 1. Mock Data (`lib/data/mock_loan_data.dart`)
- **Purpose**: Contains realistic Cambodian loan application data
- **Features**:
  - 4 sample loan applications with different statuses (Pending, Approved, Disbursed, Rejected)
  - Authentic Cambodian names, addresses, and business types
  - Realistic loan amounts in USD (common in Cambodia)
  - Portfolio officers with Khmer names
  - Family, guarantor, and asset information
  - Document URLs and timestamps

### 2. Mock Service (`lib/services/mock_loan_service.dart`)
- **Purpose**: Simulates API calls with realistic delays
- **Features**:
  - Implements all methods from `LoanApplicationService`
  - Simulates network delays (500-1500ms)
  - Provides CRUD operations for loan applications
  - Dashboard statistics calculation
  - Search and filtering capabilities
  - Error simulation for testing

### 3. Demo Screen (`lib/screens/mock_data_demo_screen.dart`)
- **Purpose**: Dedicated screen to showcase mock data
- **Features**:
  - Bilingual interface (English/Khmer)
  - Statistics overview with Khmer labels
  - Tabbed view by application status
  - Beautiful card-based layout
  - Navigation to detailed views
  - Cultural context awareness

## Configuration

### Switching Between Mock and Real Data

In `lib/main.dart`, the `AppConfig` class contains a flag:

```dart
static const bool useMockData = true; // Set to false when backend is ready
```

- **`true`**: Uses mock data service
- **`false`**: Uses real API service

### Provider Integration

The `loanApplicationServiceProvider` in `lib/providers/loan_application_provider.dart` automatically switches between services based on the configuration:

```dart
final loanApplicationServiceProvider = Provider<LoanApplicationService>((ref) {
  return AppConfig.useMockData 
      ? MockLoanApplicationService() 
      : LoanApplicationService();
});
```

## Mock Data Details

### Sample Applications

1. **Sokha Chen** - Pending Application
   - Business: Grocery Store
   - Amount: $5,000
   - Location: Phnom Penh

2. **Dara Pich** - Approved Application
   - Business: Rice Trading
   - Amount: $8,000
   - Location: Siem Reap

3. **Sophea Lim** - Disbursed Application
   - Business: Tailoring Shop
   - Amount: $3,000
   - Location: Battambang

4. **Virak Noun** - Rejected Application
   - Business: Motorcycle Repair
   - Amount: $12,000
   - Location: Kampong Cham

### Portfolio Officers

- **Chanthy Sor** - Senior Portfolio Officer
- **Pisach Keo** - Portfolio Officer
- **Sreypov Meas** - Junior Portfolio Officer

### Business Types

Common Cambodian business categories:
- Grocery Store (ហាងលក់គ្រឿងទេស)
- Rice Trading (ការជួញដូររបស់អង្ករ)
- Tailoring Shop (ហាងកាត់ដេរ)
- Motorcycle Repair (ជួសជុលម៉ូតូ)
- Street Food Vendor (លក់អាហារតាមផ្លូវ)
- And more...

### Provinces

All 25 Cambodian provinces are included with both English and Khmer names.

## Usage

### Accessing Mock Demo

1. **From Dashboard**: When `useMockData` is `true`, a data icon appears in the app bar
2. **Direct Navigation**: Use route `/mock-demo`
3. **Programmatic**: `Navigator.pushNamed(context, '/mock-demo')`

### Features Demonstrated

- **Application Workflow**: Complete loan application process
- **Status Management**: Pending → Approved → Disbursed flow
- **Cultural Context**: Cambodian names, addresses, business types
- **Bilingual Support**: English and Khmer text
- **Realistic Data**: Appropriate loan amounts and terms
- **Document Management**: Sample document URLs
- **Family Information**: Spouse and children details
- **Guarantor System**: Collateral and guarantor information
- **Asset Tracking**: Property and asset valuation

## Development Benefits

1. **No Backend Dependency**: Develop UI without waiting for API
2. **Realistic Testing**: Test with culturally appropriate data
3. **Demo Ready**: Perfect for stakeholder presentations
4. **Easy Switching**: Toggle between mock and real data
5. **Complete Workflow**: All application states represented
6. **Performance Testing**: Simulated network delays

## Customization

### Adding More Applications

In `mock_loan_data.dart`, add new entries to the `_applications` list:

```dart
LoanApplication(
  id: 'app-005',
  status: LoanStatus.pending,
  // ... other properties
),
```

### Modifying Delays

In `mock_loan_service.dart`, adjust the delay range:

```dart
Future<void> _simulateDelay() async {
  await Future.delayed(
    Duration(milliseconds: 500 + Random().nextInt(1000)), // 500-1500ms
  );
}
```

### Adding Business Types

Update the `getBusinessTypes()` method in `mock_loan_data.dart`:

```dart
static List<String> getBusinessTypes() {
  return [
    'Your New Business Type',
    // ... existing types
  ];
}
```

## Cultural Considerations

- **Names**: Authentic Cambodian names following traditional patterns
- **Addresses**: Real Cambodian provinces and districts
- **Currency**: USD amounts (commonly used in Cambodia)
- **Business Types**: Relevant to Cambodian economy
- **Language**: Khmer script support throughout
- **Family Structure**: Reflects Cambodian family dynamics

## Next Steps

1. **Backend Integration**: When API is ready, set `useMockData = false`
2. **Data Migration**: Use mock data structure as API specification
3. **Testing**: Validate real API responses match mock structure
4. **Localization**: Expand Khmer translations
5. **Performance**: Optimize for production use

## Support

This mock implementation provides a solid foundation for:
- UI development and testing
- Stakeholder demonstrations
- User acceptance testing
- Performance optimization
- Cultural validation

The data is designed to be realistic and culturally appropriate for Cambodian users while showcasing all features of the loan application system.