# Mobile Loan Application - UI Design

This document outlines the UI design for the mobile loan processing application, reflecting the detailed specifications in `UI_DATA_FLOW.md`.

## Color Palette & Theme

The UI will use the following color palette, based on the provided image:

-   **Primary Blue:** `#27498C`
-   **Dark Blue (for text and headers):** `#223D73`
-   **Primary Accent (Orange):** `#BF6D24`
-   **Secondary Accent (Lighter Orange):** `#BF622C`
-   **Warning/Error (Dark Brown/Red):** `#732306`
-   **Background:** `#FFFFFF` (White)
-   **Text:** `#223D73`

---

## General UI Components

-   **Buttons (Primary Action, e.g., Approve, Save):** Background: `#27498C`, Text: `#FFFFFF`
-   **Buttons (Secondary Action, e.g., New Application):** Background: `#BF6D24`, Text: `#FFFFFF`
-   **Buttons (Warning/Reject):** Background: `#732306`, Text: `#FFFFFF`
-   **Input Fields:** Standard with a border color of `#223D73`.
-   **Headers & Titles:** Text color `#223D73`.
-   **AI Card Background:** A light blue, derived from `#27498C` (e.g., `#E9ECF3`) to make it stand out.
-   **Status Badge (Pending):** Background `#BF6D24`, Text `#FFFFFF`
-   **Status Badge (Approved):** Background `#27498C`, Text `#FFFFFF`
-   **Status Badge (Rejected):** Background `#732306`, Text `#FFFFFF`

---

## Key Screens & Components

### A. Dashboards

#### PO Dashboard

**Screen: PO Dashboard**

```
+----------------------------------------------------+
| LC Mobile                          [User Profile]  |
|----------------------------------------------------|
|                                                    |
| +----------+ +----------+ +----------+ +----------+ |
| | Total    | | Total    | | Approved | | Approval |
| | Apps     | | Value    | | Loans    | | Rate     | |
| | 25       | | $1.2M    | | 18       | | 72%      | |
| +----------+ +----------+ +----------+ +----------+ |
|                                                    |
| **Application Status**                             |
| [|||||] Pending  [|||] Approved  [|] Rejected     |
|                                                    |
| +------------------------------------------------+ |
| | [Avatar] Sokha Chen        [Pending Badge]   | |
| |          Street Food Vendor                  | |
| |------------------------------------------------| |
| | [Icon] Amount: $5,000                          | |
| | [Icon] Date: 2024-07-15                        | |
| |                      [View Details ->]         | |
| +------------------------------------------------+ |
|                                                    |
|                      [+ New Application]           |
+----------------------------------------------------+
```

#### CPO & Teller Dashboards

-   **CPO:** Similar grid layout but only shows `Pending` applications.
-   **Teller:** Similar grid layout but only shows `Approved` applications.

### B. New Application Form (PO)

**Screen: Multi-Step Loan Application Form**

```
+----------------------------------------------------+
| [<- Back]   New Application                [Menu]  |
|----------------------------------------------------|
|                                                    |
| **Step 1 of 7: NID Scan**                          |
| [|||||.......................]                     |
|                                                    |
| +----------------------------------+               |
| | [Click to Scan National ID Card] |               |
| | (AI-powered OCR)                 |               |
| +----------------------------------+               |
|                                                    |
| Name: [Sokha Chen (pre-filled)   ]               |
| DOB:  [1985-04-12 (pre-filled)   ]               |
|                                                    |
|                                [ Next > ]          |
|                                                    |
+----------------------------------------------------+
```
-   **Navigation:** Includes "Next" and "Previous" buttons.
-   **Final Step:** Features a "Submit Application" button with the `khmer-gold-gradient`.

### C. Application Detail Page (CPO Review)

**Screen: Loan Application Review**

```
+----------------------------------------------------+
| [<- Back]   Loan Review                    [Menu]  |
|----------------------------------------------------|
| +------------------------------------------------+ |
| | [Avatar] Sokha Chen        [Pending Badge]   | |
| |          ID: app-001                         | |
| | Amount: $5,000    Term: 24 mos               | |
| +------------------------------------------------+ |
|                                                    |
| +-[AI Analysis]----------------------------------+ |
| | [Blue Gradient Background]                     | |
| | Summary: ...                                   | |
| | Risk: Medium                                   | |
| | [+] Positive Factors...                        | |
| | [-] Negative Factors...                        | |
| +------------------------------------------------+ |
|                                                    |
| > Applicant Details                                |
| > Business, Family, Assets, Guarantor...           |
|                                                    |
|      [ Approve ]      [ Reject ]                   |
|                                                    |
+----------------------------------------------------+
```
-   **AI Card:** Uses the `temple-blue-gradient` to stand out.
-   **Action Buttons:** "Approve" (Primary Color) and "Reject".
| [Take Photo of Guarantor]  [View]                  |
| [Take Photo of Collateral] [View]                  |
|                                                    |
|                                                    |
|                                                    |
|                                                    |
|                [ Complete Application ]            |
|                                                    |
+----------------------------------------------------+
```

-   **Complete Application Button:** Background `#BF6D24`, Text `#FFFFFF`

### Scene 4: CPO Review and Final Verification (CPO)

**Screen: Loan Application Review**

```
+----------------------------------------------------+
| [<- Back]   Loan Review                    [Menu]  |
|----------------------------------------------------|
|                                                    |
| **Application Details** (Read-only)                |
| > Customer Info                                    |
| > Assets & Agreement                               |
| > Guarantor Details                                |
|                                                    |
| **CPO Verification**                               |
|                                                    |
| [Take Photo of Property Title] [View]              |
| [Take Photo of Location]       [View]              |
|                                                    |
| **Decision**                                       |
|                                                    |
|      [ Approve (Yes) ]      [ Reject (No) ]        |
|                                                    |
+----------------------------------------------------+
```

-   **Approve Button:** Background `#27498C`, Text `#FFFFFF`
-   **Reject Button:** Background `#732306`, Text `#FFFFFF`

### Scene 5: Loan Disbursement (Teller)

**Screen: Loan Disbursement**

```
+----------------------------------------------------+
| [<- Back]   Disbursement                   [Menu]  |
|----------------------------------------------------|
|                                                    |
| **Loan Search**                                    |
| [ Search by Application ID or Customer Name ] [Go] |
|                                                    |
| **Loan Details**                                   |
| Customer: John Doe                                 |
| Amount: $5000                                      |
| Status: Approved                                   |
|                                                    |
|                                                    |
|                                                    |
|                                                    |
|                [ Confirm Disbursement ]            |
|                                                    |
+----------------------------------------------------+
```

-   **Confirm Disbursement Button:** Background `#27498C`, Text `#FFFFFF`