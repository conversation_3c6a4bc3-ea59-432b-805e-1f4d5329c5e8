import 'dart:io';
import 'package:image/image.dart' as img;
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;

/// Service for camera operations and image handling
class CameraService {
  static final CameraService _instance = CameraService._internal();
  factory CameraService() => _instance;
  CameraService._internal();

  /// Capture photo from camera
  Future<String> capturePhoto({
    PhotoType photoType = PhotoType.general,
    ImageQuality quality = ImageQuality.high,
  }) async {
    try {
      // Simulate camera capture
      await Future.delayed(const Duration(seconds: 1));
      
      // In production, use camera plugin like:
      // - camera package
      // - image_picker package
      // - camera_camera package
      
      // For demo, create a placeholder image
      final imagePath = await _createPlaceholderImage(photoType);
      
      // Apply quality settings
      if (quality != ImageQuality.original) {
        return await _compressImage(imagePath, quality);
      }
      
      return imagePath;
    } catch (e) {
      throw CameraException('Failed to capture photo: $e');
    }
  }

  /// Pick image from gallery
  Future<String> pickFromGallery({
    ImageQuality quality = ImageQuality.high,
  }) async {
    try {
      // Simulate gallery picker
      await Future.delayed(const Duration(milliseconds: 500));
      
      // In production, use image_picker package
      // final picker = ImagePicker();
      // final XFile? image = await picker.pickImage(source: ImageSource.gallery);
      
      // For demo, create a placeholder image
      final imagePath = await _createPlaceholderImage(PhotoType.general);
      
      // Apply quality settings
      if (quality != ImageQuality.original) {
        return await _compressImage(imagePath, quality);
      }
      
      return imagePath;
    } catch (e) {
      throw CameraException('Failed to pick image: $e');
    }
  }

  /// Capture multiple photos
  Future<List<String>> captureMultiplePhotos(
    int count, {
    PhotoType photoType = PhotoType.general,
    ImageQuality quality = ImageQuality.high,
  }) async {
    final photos = <String>[];
    
    for (int i = 0; i < count; i++) {
      try {
        final photo = await capturePhoto(
          photoType: photoType,
          quality: quality,
        );
        photos.add(photo);
      } catch (e) {
        // Continue capturing other photos even if one fails
        print('Failed to capture photo ${i + 1}: $e');
      }
    }
    
    return photos;
  }

  /// Compress image to reduce file size
  Future<String> _compressImage(String imagePath, ImageQuality quality) async {
    try {
      final file = File(imagePath);
      final bytes = await file.readAsBytes();
      
      // Decode the image
      final image = img.decodeImage(bytes);
      if (image == null) {
        throw const CameraException('Invalid image format');
      }

      // Determine compression settings based on quality
      int maxWidth, maxHeight, jpegQuality;
      
      switch (quality) {
        case ImageQuality.low:
          maxWidth = 640;
          maxHeight = 480;
          jpegQuality = 60;
          break;
        case ImageQuality.medium:
          maxWidth = 1280;
          maxHeight = 960;
          jpegQuality = 75;
          break;
        case ImageQuality.high:
          maxWidth = 1920;
          maxHeight = 1440;
          jpegQuality = 85;
          break;
        case ImageQuality.original:
          return imagePath; // No compression
      }
      
      // Resize image if necessary
      var compressedImage = image;
      if (image.width > maxWidth || image.height > maxHeight) {
        compressedImage = img.copyResize(
          image,
          width: image.width > image.height ? maxWidth : null,
          height: image.height > image.width ? maxHeight : null,
        );
      }
      
      // Encode with JPEG compression
      final compressedBytes = img.encodeJpg(compressedImage, quality: jpegQuality);
      
      // Save compressed image
      final compressedPath = await _getUniqueFilePath('compressed_image', 'jpg');
      await File(compressedPath).writeAsBytes(compressedBytes);
      
      // Delete original if different
      if (compressedPath != imagePath) {
        await file.delete();
      }
      
      return compressedPath;
    } catch (e) {
      throw CameraException('Image compression failed: $e');
    }
  }

  /// Create placeholder image for demo purposes
  Future<String> _createPlaceholderImage(PhotoType photoType) async {
    try {
      // Create a simple colored image based on photo type
      const width = 800;
      const height = 600;
      
      final image = img.Image(width: width, height: height);
      
      // Fill with color based on photo type
      img.Color color;
      switch (photoType) {
        case PhotoType.nid:
          color = img.ColorRgb8(100, 150, 200); // Blue for NID
          break;
        case PhotoType.applicant:
          color = img.ColorRgb8(150, 200, 150); // Green for applicant
          break;
        case PhotoType.guarantor:
          color = img.ColorRgb8(200, 150, 100); // Orange for guarantor
          break;
        case PhotoType.business:
          color = img.ColorRgb8(200, 200, 100); // Yellow for business
          break;
        case PhotoType.document:
          color = img.ColorRgb8(180, 180, 180); // Gray for documents
          break;
        case PhotoType.general:
        default:
          color = img.ColorRgb8(150, 150, 150); // Default gray
          break;
      }
      
      img.fill(image, color: color);
      
      // Add some text to indicate photo type
      final text = photoType.displayName;
      // Note: img package doesn't have built-in text rendering
      // In production, use a proper image manipulation library
      
      // Encode to JPEG
      final bytes = img.encodeJpg(image, quality: 85);
      
      // Save to file
      final filePath = await _getUniqueFilePath(
        '${photoType.name}_photo',
        'jpg',
      );
      
      await File(filePath).writeAsBytes(bytes);
      
      return filePath;
    } catch (e) {
      throw CameraException('Failed to create placeholder image: $e');
    }
  }

  /// Get unique file path for saving images
  Future<String> _getUniqueFilePath(String baseName, String extension) async {
    final directory = await getApplicationDocumentsDirectory();
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final fileName = '${baseName}_$timestamp.$extension';
    return path.join(directory.path, 'photos', fileName);
  }

  /// Validate image file
  Future<ImageValidationResult> validateImage(String imagePath) async {
    try {
      final file = File(imagePath);
      
      if (!await file.exists()) {
        return const ImageValidationResult(
          isValid: false,
          error: 'Image file does not exist',
        );
      }
      
      final bytes = await file.readAsBytes();
      final image = img.decodeImage(bytes);
      
      if (image == null) {
        return const ImageValidationResult(
          isValid: false,
          error: 'Invalid image format',
        );
      }
      
      // Check file size (max 10MB)
      if (bytes.length > 10 * 1024 * 1024) {
        return const ImageValidationResult(
          isValid: false,
          error: 'Image file is too large (max 10MB)',
        );
      }
      
      // Check dimensions (min 200x200, max 4000x4000)
      if (image.width < 200 || image.height < 200) {
        return const ImageValidationResult(
          isValid: false,
          error: 'Image resolution is too low (min 200x200)',
        );
      }
      
      if (image.width > 4000 || image.height > 4000) {
        return const ImageValidationResult(
          isValid: false,
          error: 'Image resolution is too high (max 4000x4000)',
        );
      }
      
      // Check aspect ratio for certain photo types
      final aspectRatio = image.width / image.height;
      if (aspectRatio < 0.5 || aspectRatio > 2.0) {
        return ImageValidationResult(
          isValid: true,
          warning: 'Unusual aspect ratio detected',
          imageInfo: ImageInfo(
            width: image.width,
            height: image.height,
            fileSize: bytes.length,
            format: _getImageFormat(imagePath),
          ),
        );
      }
      
      return ImageValidationResult(
        isValid: true,
        imageInfo: ImageInfo(
          width: image.width,
          height: image.height,
          fileSize: bytes.length,
          format: _getImageFormat(imagePath),
        ),
      );
    } catch (e) {
      return ImageValidationResult(
        isValid: false,
        error: 'Image validation failed: $e',
      );
    }
  }

  /// Get image format from file path
  String _getImageFormat(String filePath) {
    final extension = path.extension(filePath).toLowerCase();
    switch (extension) {
      case '.jpg':
      case '.jpeg':
        return 'JPEG';
      case '.png':
        return 'PNG';
      case '.webp':
        return 'WebP';
      case '.bmp':
        return 'BMP';
      default:
        return 'Unknown';
    }
  }

  /// Rotate image
  Future<String> rotateImage(String imagePath, int degrees) async {
    try {
      final file = File(imagePath);
      final bytes = await file.readAsBytes();
      
      final image = img.decodeImage(bytes);
      if (image == null) {
        throw const CameraException('Invalid image format');
      }
      
      // Rotate image
      final rotatedImage = img.copyRotate(image, angle: degrees.toDouble());
      
      // Save rotated image
      final rotatedBytes = img.encodeJpg(rotatedImage, quality: 85);
      final rotatedPath = await _getUniqueFilePath('rotated_image', 'jpg');
      await File(rotatedPath).writeAsBytes(rotatedBytes);
      
      return rotatedPath;
    } catch (e) {
      throw CameraException('Image rotation failed: $e');
    }
  }

  /// Crop image
  Future<String> cropImage(
    String imagePath,
    int x,
    int y,
    int width,
    int height,
  ) async {
    try {
      final file = File(imagePath);
      final bytes = await file.readAsBytes();
      
      final image = img.decodeImage(bytes);
      if (image == null) {
        throw const CameraException('Invalid image format');
      }
      
      // Crop image
      final croppedImage = img.copyCrop(
        image,
        x: x,
        y: y,
        width: width,
        height: height,
      );
      
      // Save cropped image
      final croppedBytes = img.encodeJpg(croppedImage, quality: 85);
      final croppedPath = await _getUniqueFilePath('cropped_image', 'jpg');
      await File(croppedPath).writeAsBytes(croppedBytes);
      
      return croppedPath;
    } catch (e) {
      throw CameraException('Image cropping failed: $e');
    }
  }

  /// Delete image file
  Future<void> deleteImage(String imagePath) async {
    try {
      final file = File(imagePath);
      if (await file.exists()) {
        await file.delete();
      }
    } catch (e) {
      throw CameraException('Failed to delete image: $e');
    }
  }

  /// Get image thumbnail
  Future<String> createThumbnail(
    String imagePath, {
    int maxWidth = 200,
    int maxHeight = 200,
  }) async {
    try {
      final file = File(imagePath);
      final bytes = await file.readAsBytes();
      
      final image = img.decodeImage(bytes);
      if (image == null) {
        throw const CameraException('Invalid image format');
      }
      
      // Create thumbnail
      final thumbnail = img.copyResize(
        image,
        width: maxWidth,
        height: maxHeight,
      );
      
      // Save thumbnail
      final thumbnailBytes = img.encodeJpg(thumbnail, quality: 70);
      final thumbnailPath = await _getUniqueFilePath('thumbnail', 'jpg');
      await File(thumbnailPath).writeAsBytes(thumbnailBytes);
      
      return thumbnailPath;
    } catch (e) {
      throw CameraException('Thumbnail creation failed: $e');
    }
  }

  /// Clean up old photos
  Future<void> cleanupOldPhotos({int maxAgeInDays = 30}) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final photosDir = Directory(path.join(directory.path, 'photos'));
      
      if (!await photosDir.exists()) return;
      
      final cutoffDate = DateTime.now().subtract(Duration(days: maxAgeInDays));
      
      await for (final entity in photosDir.list()) {
        if (entity is File) {
          final stat = await entity.stat();
          if (stat.modified.isBefore(cutoffDate)) {
            await entity.delete();
          }
        }
      }
    } catch (e) {
      print('Failed to cleanup old photos: $e');
    }
  }
}

/// Types of photos that can be captured
enum PhotoType {
  nid,
  applicant,
  guarantor,
  business,
  document,
  general,
}

extension PhotoTypeExtension on PhotoType {
  String get displayName {
    switch (this) {
      case PhotoType.nid:
        return 'NID Photo';
      case PhotoType.applicant:
        return 'Applicant Photo';
      case PhotoType.guarantor:
        return 'Guarantor Photo';
      case PhotoType.business:
        return 'Business Photo';
      case PhotoType.document:
        return 'Document Photo';
      case PhotoType.general:
        return 'General Photo';
    }
  }
  
  String get description {
    switch (this) {
      case PhotoType.nid:
        return 'Take a clear photo of the National ID card';
      case PhotoType.applicant:
        return 'Take a passport-style photo of the applicant';
      case PhotoType.guarantor:
        return 'Take a passport-style photo of the guarantor';
      case PhotoType.business:
        return 'Take a photo of the business premises';
      case PhotoType.document:
        return 'Take a photo of the document';
      case PhotoType.general:
        return 'Take a general photo';
    }
  }
}

/// Image quality settings
enum ImageQuality {
  low,
  medium,
  high,
  original,
}

/// Image information
class ImageInfo {
  final int width;
  final int height;
  final int fileSize;
  final String format;

  const ImageInfo({
    required this.width,
    required this.height,
    required this.fileSize,
    required this.format,
  });
  
  String get fileSizeFormatted {
    if (fileSize < 1024) {
      return '$fileSize B';
    } else if (fileSize < 1024 * 1024) {
      return '${(fileSize / 1024).toStringAsFixed(1)} KB';
    } else {
      return '${(fileSize / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
  }
  
  String get resolution => '${width}x$height';
  
  double get aspectRatio => width / height;
}

/// Result of image validation
class ImageValidationResult {
  final bool isValid;
  final String? error;
  final String? warning;
  final ImageInfo? imageInfo;

  const ImageValidationResult({
    required this.isValid,
    this.error,
    this.warning,
    this.imageInfo,
  });
}

/// Custom exception for camera operations
class CameraException implements Exception {
  final String message;
  final String? code;

  const CameraException(this.message, [this.code]);

  @override
  String toString() => 'CameraException: $message';
}

/// Camera configuration
class CameraConfig {
  final ImageQuality defaultQuality;
  final bool enableFlash;
  final bool enableAutoFocus;
  final PhotoType defaultPhotoType;
  final int maxPhotosPerSession;

  const CameraConfig({
    this.defaultQuality = ImageQuality.high,
    this.enableFlash = true,
    this.enableAutoFocus = true,
    this.defaultPhotoType = PhotoType.general,
    this.maxPhotosPerSession = 10,
  });
}