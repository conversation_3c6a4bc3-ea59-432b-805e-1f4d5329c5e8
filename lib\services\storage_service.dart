import 'dart:convert';
import 'package:flutter/foundation.dart';
import '../models/loan_application.dart';

/// Comprehensive storage service for local data persistence and caching
class StorageService {
  static final StorageService _instance = StorageService._internal();
  factory StorageService() => _instance;
  StorageService._internal();

  bool _isInitialized = false;
  String? _databasePath;
  final Map<String, dynamic> _memoryCache = {};
  final Map<String, DateTime> _cacheTimestamps = {};
  final Map<String, Duration> _cacheTTL = {};
  
  // Storage configuration
  static const Duration _defaultCacheTTL = Duration(hours: 1);
  static const int _maxCacheSize = 100;
  static const int _maxDatabaseSize = 50 * 1024 * 1024; // 50MB

  /// Initialize storage service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Initialize database
      await _initializeDatabase();
      
      // Initialize shared preferences
      await _initializeSharedPreferences();
      
      // Initialize file storage
      await _initializeFileStorage();
      
      // Setup cache cleanup
      _setupCacheCleanup();
      
      // Load cached data
      await _loadCachedData();
      
      _isInitialized = true;
      print('StorageService initialized successfully');
    } catch (e) {
      throw StorageException('Failed to initialize storage service: $e');
    }
  }

  /// Initialize database (SQLite)
  Future<void> _initializeDatabase() async {
    try {
      // In production, use sqflite package:
      // final databasesPath = await getDatabasesPath();
      // _databasePath = join(databasesPath, 'loan_app.db');
      // 
      // _database = await openDatabase(
      //   _databasePath!,
      //   version: 1,
      //   onCreate: _createDatabase,
      //   onUpgrade: _upgradeDatabase,
      // );
      
      _databasePath = 'demo_database_path';
      print('Database initialized at: $_databasePath');
    } catch (e) {
      throw StorageException('Failed to initialize database: $e');
    }
  }

  /// Initialize shared preferences
  Future<void> _initializeSharedPreferences() async {
    try {
      // In production, use shared_preferences package:
      // _sharedPreferences = await SharedPreferences.getInstance();
      
      print('SharedPreferences initialized');
    } catch (e) {
      throw StorageException('Failed to initialize SharedPreferences: $e');
    }
  }

  /// Initialize file storage
  Future<void> _initializeFileStorage() async {
    try {
      // In production, use path_provider package:
      // _documentsDirectory = await getApplicationDocumentsDirectory();
      // _cacheDirectory = await getTemporaryDirectory();
      
      print('File storage initialized');
    } catch (e) {
      throw StorageException('Failed to initialize file storage: $e');
    }
  }

  /// Setup cache cleanup timer
  void _setupCacheCleanup() {
    // In production, setup periodic cache cleanup:
    // Timer.periodic(Duration(minutes: 30), (_) => _cleanupExpiredCache());
  }

  /// Load cached data from persistent storage
  Future<void> _loadCachedData() async {
    try {
      // Load frequently accessed data into memory cache
      await _loadUserPreferences();
      await _loadRecentApplications();
    } catch (e) {
      print('Failed to load cached data: $e');
    }
  }

  /// Store data with caching
  Future<void> storeData(
    String key,
    dynamic data, {
    StorageType type = StorageType.cache,
    Duration? ttl,
    bool encrypt = false,
  }) async {
    if (!_isInitialized) {
      throw const StorageException('Storage service not initialized');
    }

    try {
      final serializedData = _serializeData(data);
      final dataToStore = encrypt ? _encryptData(serializedData) : serializedData;

      switch (type) {
        case StorageType.cache:
          await _storeInCache(key, dataToStore, ttl ?? _defaultCacheTTL);
          break;
        case StorageType.persistent:
          await _storeInDatabase(key, dataToStore);
          break;
        case StorageType.preferences:
          await _storeInPreferences(key, dataToStore);
          break;
        case StorageType.file:
          await _storeInFile(key, dataToStore);
          break;
      }

      if (kDebugMode) {
        print('💾 Stored data: $key (${type.name})');
      }
    } catch (e) {
      throw StorageException('Failed to store data for key $key: $e');
    }
  }

  /// Retrieve data from storage
  Future<T?> getData<T>(
    String key, {
    StorageType type = StorageType.cache,
    bool decrypt = false,
    T? defaultValue,
  }) async {
    if (!_isInitialized) {
      throw const StorageException('Storage service not initialized');
    }

    try {
      String? rawData;

      switch (type) {
        case StorageType.cache:
          rawData = await _getFromCache(key);
          break;
        case StorageType.persistent:
          rawData = await _getFromDatabase(key);
          break;
        case StorageType.preferences:
          rawData = await _getFromPreferences(key);
          break;
        case StorageType.file:
          rawData = await _getFromFile(key);
          break;
      }

      if (rawData == null) {
        return defaultValue;
      }

      final dataToDeserialize = decrypt ? _decryptData(rawData) : rawData;
      return _deserializeData<T>(dataToDeserialize);
    } catch (e) {
      print('Failed to get data for key $key: $e');
      return defaultValue;
    }
  }

  /// Store in memory cache
  Future<void> _storeInCache(String key, String data, Duration ttl) async {
    _memoryCache[key] = data;
    _cacheTimestamps[key] = DateTime.now();
    _cacheTTL[key] = ttl;

    // Cleanup if cache is too large
    if (_memoryCache.length > _maxCacheSize) {
      await _cleanupOldestCacheEntries();
    }
  }

  /// Get from memory cache
  Future<String?> _getFromCache(String key) async {
    final timestamp = _cacheTimestamps[key];
    final ttl = _cacheTTL[key];

    if (timestamp == null || ttl == null) {
      return null;
    }

    if (DateTime.now().difference(timestamp) > ttl) {
      // Cache expired
      _memoryCache.remove(key);
      _cacheTimestamps.remove(key);
      _cacheTTL.remove(key);
      return null;
    }

    return _memoryCache[key];
  }

  /// Store in database
  Future<void> _storeInDatabase(String key, String data) async {
    try {
      // In production, use sqflite:
      // await _database.insert(
      //   'storage',
      //   {
      //     'key': key,
      //     'data': data,
      //     'created_at': DateTime.now().toIso8601String(),
      //     'updated_at': DateTime.now().toIso8601String(),
      //   },
      //   conflictAlgorithm: ConflictAlgorithm.replace,
      // );
      
      print('Stored in database: $key');
    } catch (e) {
      throw StorageException('Failed to store in database: $e');
    }
  }

  /// Get from database
  Future<String?> _getFromDatabase(String key) async {
    try {
      // In production, use sqflite:
      // final result = await _database.query(
      //   'storage',
      //   where: 'key = ?',
      //   whereArgs: [key],
      //   limit: 1,
      // );
      // 
      // if (result.isNotEmpty) {
      //   return result.first['data'] as String;
      // }
      
      return null;
    } catch (e) {
      print('Failed to get from database: $e');
      return null;
    }
  }

  /// Store in shared preferences
  Future<void> _storeInPreferences(String key, String data) async {
    try {
      // In production, use SharedPreferences:
      // await _sharedPreferences.setString(key, data);
      
      print('Stored in preferences: $key');
    } catch (e) {
      throw StorageException('Failed to store in preferences: $e');
    }
  }

  /// Get from shared preferences
  Future<String?> _getFromPreferences(String key) async {
    try {
      // In production, use SharedPreferences:
      // return _sharedPreferences.getString(key);
      
      return null;
    } catch (e) {
      print('Failed to get from preferences: $e');
      return null;
    }
  }

  /// Store in file
  Future<void> _storeInFile(String key, String data) async {
    try {
      // In production, use File I/O:
      // final file = File(join(_documentsDirectory.path, '$key.json'));
      // await file.writeAsString(data);
      
      print('Stored in file: $key');
    } catch (e) {
      throw StorageException('Failed to store in file: $e');
    }
  }

  /// Get from file
  Future<String?> _getFromFile(String key) async {
    try {
      // In production, use File I/O:
      // final file = File(join(_documentsDirectory.path, '$key.json'));
      // if (await file.exists()) {
      //   return await file.readAsString();
      // }
      
      return null;
    } catch (e) {
      print('Failed to get from file: $e');
      return null;
    }
  }

  /// Loan application specific methods
  
  /// Store loan application
  Future<void> storeLoanApplication(LoanApplication application) async {
    await storeData(
      'loan_application_${application.id}',
      application.toJson(),
      type: StorageType.persistent,
    );
    
    // Also cache for quick access
    await storeData(
      'cached_application_${application.id}',
      application.toJson(),
      type: StorageType.cache,
      ttl: const Duration(minutes: 30),
    );
  }

  /// Get loan application
  Future<LoanApplication?> getLoanApplication(String applicationId) async {
    // Try cache first
    final cachedData = await getData<Map<String, dynamic>>(
      'cached_application_$applicationId',
      type: StorageType.cache,
    );
    
    if (cachedData != null) {
      return LoanApplication.fromJson(cachedData);
    }
    
    // Fallback to persistent storage
    final persistentData = await getData<Map<String, dynamic>>(
      'loan_application_$applicationId',
      type: StorageType.persistent,
    );
    
    if (persistentData != null) {
      final application = LoanApplication.fromJson(persistentData);
      
      // Cache for future access
      await storeData(
        'cached_application_$applicationId',
        application.toJson(),
        type: StorageType.cache,
        ttl: const Duration(minutes: 30),
      );
      
      return application;
    }
    
    return null;
  }

  /// Store multiple loan applications
  Future<void> storeLoanApplications(List<LoanApplication> applications) async {
    final applicationsData = applications.map((app) => app.toJson()).toList();
    
    await storeData(
      'loan_applications_list',
      applicationsData,
      type: StorageType.persistent,
    );
    
    // Cache recent applications
    final recentApplications = applications.take(10).toList();
    await storeData(
      'recent_applications',
      recentApplications.map((app) => app.toJson()).toList(),
      type: StorageType.cache,
      ttl: const Duration(minutes: 15),
    );
  }

  /// Get all loan applications
  Future<List<LoanApplication>> getAllLoanApplications() async {
    final data = await getData<List<dynamic>>(
      'loan_applications_list',
      type: StorageType.persistent,
      defaultValue: [],
    );
    
    return data!.map((json) => LoanApplication.fromJson(json)).toList();
  }

  /// Store user preferences
  Future<void> storeUserPreferences(Map<String, dynamic> preferences) async {
    await storeData(
      'user_preferences',
      preferences,
      type: StorageType.preferences,
    );
  }

  /// Get user preferences
  Future<Map<String, dynamic>> getUserPreferences() async {
    return await getData<Map<String, dynamic>>(
      'user_preferences',
      type: StorageType.preferences,
      defaultValue: {},
    ) ?? {};
  }

  /// Store offline data for sync
  Future<void> storeOfflineData(
    String key,
    Map<String, dynamic> data,
    OfflineAction action,
  ) async {
    final offlineEntry = {
      'key': key,
      'data': data,
      'action': action.name,
      'timestamp': DateTime.now().toIso8601String(),
      'synced': false,
    };
    
    // Get existing offline data
    final existingData = await getData<List<dynamic>>(
      'offline_sync_queue',
      type: StorageType.persistent,
      defaultValue: [],
    ) ?? [];
    
    existingData.add(offlineEntry);
    
    await storeData(
      'offline_sync_queue',
      existingData,
      type: StorageType.persistent,
    );
  }

  /// Get offline sync queue
  Future<List<OfflineDataEntry>> getOfflineSyncQueue() async {
    final data = await getData<List<dynamic>>(
      'offline_sync_queue',
      type: StorageType.persistent,
      defaultValue: [],
    ) ?? [];
    
    return data.map((json) => OfflineDataEntry.fromJson(json)).toList();
  }

  /// Mark offline data as synced
  Future<void> markOfflineDataSynced(String key) async {
    final queue = await getOfflineSyncQueue();
    final updatedQueue = queue.map((entry) {
      if (entry.key == key) {
        return entry.copyWith(synced: true);
      }
      return entry;
    }).toList();
    
    await storeData(
      'offline_sync_queue',
      updatedQueue.map((e) => e.toJson()).toList(),
      type: StorageType.persistent,
    );
  }

  /// Clear synced offline data
  Future<void> clearSyncedOfflineData() async {
    final queue = await getOfflineSyncQueue();
    final unsyncedQueue = queue.where((entry) => !entry.synced).toList();
    
    await storeData(
      'offline_sync_queue',
      unsyncedQueue.map((e) => e.toJson()).toList(),
      type: StorageType.persistent,
    );
  }

  /// Store image file
  Future<String> storeImageFile(String fileName, Uint8List imageData) async {
    try {
      // In production, use path_provider and File I/O:
      // final directory = await getApplicationDocumentsDirectory();
      // final imagesDir = Directory(join(directory.path, 'images'));
      // if (!await imagesDir.exists()) {
      //   await imagesDir.create(recursive: true);
      // }
      // 
      // final file = File(join(imagesDir.path, fileName));
      // await file.writeAsBytes(imageData);
      // return file.path;
      
      // For demo purposes, return a mock path
      final mockPath = '/mock/images/$fileName';
      print('Stored image: $mockPath');
      return mockPath;
    } catch (e) {
      throw StorageException('Failed to store image file: $e');
    }
  }

  /// Get image file
  Future<Uint8List?> getImageFile(String filePath) async {
    try {
      // In production, use File I/O:
      // final file = File(filePath);
      // if (await file.exists()) {
      //   return await file.readAsBytes();
      // }
      
      return null;
    } catch (e) {
      print('Failed to get image file: $e');
      return null;
    }
  }

  /// Delete data
  Future<void> deleteData(String key, {StorageType type = StorageType.cache}) async {
    try {
      switch (type) {
        case StorageType.cache:
          _memoryCache.remove(key);
          _cacheTimestamps.remove(key);
          _cacheTTL.remove(key);
          break;
        case StorageType.persistent:
          // await _database.delete('storage', where: 'key = ?', whereArgs: [key]);
          break;
        case StorageType.preferences:
          // await _sharedPreferences.remove(key);
          break;
        case StorageType.file:
          // final file = File(join(_documentsDirectory.path, '$key.json'));
          // if (await file.exists()) {
          //   await file.delete();
          // }
          break;
      }
      
      print('Deleted data: $key (${type.name})');
    } catch (e) {
      print('Failed to delete data for key $key: $e');
    }
  }

  /// Clear all data
  Future<void> clearAllData({StorageType? type}) async {
    try {
      if (type == null || type == StorageType.cache) {
        _memoryCache.clear();
        _cacheTimestamps.clear();
        _cacheTTL.clear();
      }
      
      if (type == null || type == StorageType.persistent) {
        // await _database.delete('storage');
      }
      
      if (type == null || type == StorageType.preferences) {
        // await _sharedPreferences.clear();
      }
      
      if (type == null || type == StorageType.file) {
        // Clear all files in documents directory
      }
      
      print('Cleared all data${type != null ? ' (${type.name})' : ''}');
    } catch (e) {
      print('Failed to clear data: $e');
    }
  }

  /// Get storage statistics
  Future<StorageStatistics> getStorageStatistics() async {
    try {
      // In production, calculate actual storage usage
      final cacheSize = _memoryCache.length;
      const databaseSize = 0; // Calculate from actual database
      const fileSize = 0; // Calculate from files
      
      return StorageStatistics(
        cacheEntries: cacheSize,
        databaseSize: databaseSize,
        fileStorageSize: fileSize,
        totalSize: databaseSize + fileSize,
        lastCleanup: DateTime.now().subtract(const Duration(hours: 1)),
      );
    } catch (e) {
      throw StorageException('Failed to get storage statistics: $e');
    }
  }

  /// Cleanup expired cache entries
  Future<void> _cleanupExpiredCache() async {
    final now = DateTime.now();
    final expiredKeys = <String>[];
    
    for (final entry in _cacheTimestamps.entries) {
      final key = entry.key;
      final timestamp = entry.value;
      final ttl = _cacheTTL[key];
      
      if (ttl != null && now.difference(timestamp) > ttl) {
        expiredKeys.add(key);
      }
    }
    
    for (final key in expiredKeys) {
      _memoryCache.remove(key);
      _cacheTimestamps.remove(key);
      _cacheTTL.remove(key);
    }
    
    if (expiredKeys.isNotEmpty) {
      print('Cleaned up ${expiredKeys.length} expired cache entries');
    }
  }

  /// Cleanup oldest cache entries
  Future<void> _cleanupOldestCacheEntries() async {
    final entries = _cacheTimestamps.entries.toList()
      ..sort((a, b) => a.value.compareTo(b.value));
    
    final entriesToRemove = entries.take(_memoryCache.length - _maxCacheSize + 10);
    
    for (final entry in entriesToRemove) {
      final key = entry.key;
      _memoryCache.remove(key);
      _cacheTimestamps.remove(key);
      _cacheTTL.remove(key);
    }
  }

  /// Load user preferences into cache
  Future<void> _loadUserPreferences() async {
    final preferences = await getUserPreferences();
    if (preferences.isNotEmpty) {
      await _storeInCache(
        'user_preferences_cache',
        jsonEncode(preferences),
        const Duration(hours: 24),
      );
    }
  }

  /// Load recent applications into cache
  Future<void> _loadRecentApplications() async {
    final applications = await getAllLoanApplications();
    final recentApplications = applications.take(5).toList();
    
    if (recentApplications.isNotEmpty) {
      await _storeInCache(
        'recent_applications_cache',
        jsonEncode(recentApplications.map((app) => app.toJson()).toList()),
        const Duration(minutes: 30),
      );
    }
  }

  /// Serialize data to JSON string
  String _serializeData(dynamic data) {
    try {
      return jsonEncode(data);
    } catch (e) {
      throw StorageException('Failed to serialize data: $e');
    }
  }

  /// Deserialize data from JSON string
  T? _deserializeData<T>(String data) {
    try {
      final decoded = jsonDecode(data);
      return decoded as T;
    } catch (e) {
      throw StorageException('Failed to deserialize data: $e');
    }
  }

  /// Encrypt data (placeholder implementation)
  String _encryptData(String data) {
    // In production, use proper encryption
    // For demo purposes, use simple base64 encoding
    return base64Encode(utf8.encode(data));
  }

  /// Decrypt data (placeholder implementation)
  String _decryptData(String encryptedData) {
    // In production, use proper decryption
    // For demo purposes, use simple base64 decoding
    return utf8.decode(base64Decode(encryptedData));
  }

  /// Check if storage service is initialized
  bool get isInitialized => _isInitialized;

  /// Get cache size
  int get cacheSize => _memoryCache.length;

  /// Dispose resources
  void dispose() {
    _memoryCache.clear();
    _cacheTimestamps.clear();
    _cacheTTL.clear();
  }
}

/// Storage types
enum StorageType {
  cache,      // In-memory cache
  persistent, // SQLite database
  preferences, // SharedPreferences
  file,       // File system
}

/// Offline actions
enum OfflineAction {
  create,
  update,
  delete,
}

/// Offline data entry
class OfflineDataEntry {
  final String key;
  final Map<String, dynamic> data;
  final OfflineAction action;
  final DateTime timestamp;
  final bool synced;

  const OfflineDataEntry({
    required this.key,
    required this.data,
    required this.action,
    required this.timestamp,
    required this.synced,
  });

  OfflineDataEntry copyWith({
    String? key,
    Map<String, dynamic>? data,
    OfflineAction? action,
    DateTime? timestamp,
    bool? synced,
  }) {
    return OfflineDataEntry(
      key: key ?? this.key,
      data: data ?? this.data,
      action: action ?? this.action,
      timestamp: timestamp ?? this.timestamp,
      synced: synced ?? this.synced,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'key': key,
      'data': data,
      'action': action.name,
      'timestamp': timestamp.toIso8601String(),
      'synced': synced,
    };
  }

  factory OfflineDataEntry.fromJson(Map<String, dynamic> json) {
    return OfflineDataEntry(
      key: json['key'],
      data: Map<String, dynamic>.from(json['data']),
      action: OfflineAction.values.firstWhere(
        (e) => e.name == json['action'],
        orElse: () => OfflineAction.create,
      ),
      timestamp: DateTime.parse(json['timestamp']),
      synced: json['synced'] ?? false,
    );
  }
}

/// Storage statistics
class StorageStatistics {
  final int cacheEntries;
  final int databaseSize;
  final int fileStorageSize;
  final int totalSize;
  final DateTime lastCleanup;

  const StorageStatistics({
    required this.cacheEntries,
    required this.databaseSize,
    required this.fileStorageSize,
    required this.totalSize,
    required this.lastCleanup,
  });

  Map<String, dynamic> toJson() {
    return {
      'cache_entries': cacheEntries,
      'database_size': databaseSize,
      'file_storage_size': fileStorageSize,
      'total_size': totalSize,
      'last_cleanup': lastCleanup.toIso8601String(),
    };
  }

  String get formattedTotalSize {
    if (totalSize < 1024) return '${totalSize}B';
    if (totalSize < 1024 * 1024) return '${(totalSize / 1024).toStringAsFixed(1)}KB';
    return '${(totalSize / (1024 * 1024)).toStringAsFixed(1)}MB';
  }
}

/// Custom exception for storage operations
class StorageException implements Exception {
  final String message;
  final String? code;

  const StorageException(this.message, [this.code]);

  @override
  String toString() => 'StorageException: $message';
}