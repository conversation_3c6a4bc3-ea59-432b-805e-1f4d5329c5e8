import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'storage_service.dart';

/// Comprehensive theme service for managing app's visual design system
class ThemeService {
  static final ThemeService _instance = ThemeService._internal();
  factory ThemeService() => _instance;
  ThemeService._internal();

  final StorageService _storageService = StorageService();
  
  ThemeMode _currentThemeMode = ThemeMode.system;
  AppColorScheme _currentColorScheme = AppColorScheme.blue;
  bool _isInitialized = false;

  /// Initialize theme service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _loadThemePreferences();
      _isInitialized = true;
      print('ThemeService initialized successfully');
    } catch (e) {
      print('Failed to initialize ThemeService: $e');
    }
  }

  /// Load theme preferences from storage
  Future<void> _loadThemePreferences() async {
    try {
      final themeMode = await _storageService.getData<String>(
        'theme_mode',
        type: StorageType.preferences,
      );
      
      final colorScheme = await _storageService.getData<String>(
        'color_scheme',
        type: StorageType.preferences,
      );

      if (themeMode != null) {
        _currentThemeMode = ThemeMode.values.firstWhere(
          (mode) => mode.name == themeMode,
          orElse: () => ThemeMode.system,
        );
      }

      if (colorScheme != null) {
        _currentColorScheme = AppColorScheme.values.firstWhere(
          (scheme) => scheme.name == colorScheme,
          orElse: () => AppColorScheme.blue,
        );
      }
    } catch (e) {
      print('Failed to load theme preferences: $e');
    }
  }

  /// Save theme preferences to storage
  Future<void> _saveThemePreferences() async {
    try {
      await _storageService.storeData(
        'theme_mode',
        _currentThemeMode.name,
        type: StorageType.preferences,
      );
      
      await _storageService.storeData(
        'color_scheme',
        _currentColorScheme.name,
        type: StorageType.preferences,
      );
    } catch (e) {
      print('Failed to save theme preferences: $e');
    }
  }

  /// Get current theme mode
  ThemeMode get themeMode => _currentThemeMode;

  /// Get current color scheme
  AppColorScheme get colorScheme => _currentColorScheme;

  /// Set theme mode
  Future<void> setThemeMode(ThemeMode mode) async {
    _currentThemeMode = mode;
    await _saveThemePreferences();
  }

  /// Set color scheme
  Future<void> setColorScheme(AppColorScheme scheme) async {
    _currentColorScheme = scheme;
    await _saveThemePreferences();
  }

  /// Get light theme data
  ThemeData get lightTheme {
    final colorScheme = _getColorScheme(false);
    
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,
      colorScheme: colorScheme,
      
      // Typography
      textTheme: _getTextTheme(colorScheme),
      
      // App Bar Theme
      appBarTheme: AppBarTheme(
        backgroundColor: colorScheme.surface,
        foregroundColor: colorScheme.onSurface,
        elevation: 0,
        centerTitle: true,
        titleTextStyle: _getTextTheme(colorScheme).titleLarge?.copyWith(
          fontWeight: FontWeight.w600,
          color: colorScheme.onSurface,
        ),
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.dark,
          statusBarBrightness: Brightness.light,
        ),
      ),
      
      // Card Theme
      cardTheme: CardTheme(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppSpacing.radiusLarge),
        ),
        color: colorScheme.surface,
        surfaceTintColor: colorScheme.surfaceTint,
      ),
      
      // Elevated Button Theme
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: colorScheme.primary,
          foregroundColor: colorScheme.onPrimary,
          elevation: 2,
          padding: const EdgeInsets.symmetric(
            horizontal: AppSpacing.large,
            vertical: AppSpacing.medium,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppSpacing.radiusMedium),
          ),
          textStyle: _getTextTheme(colorScheme).labelLarge?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      
      // Outlined Button Theme
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: colorScheme.primary,
          side: BorderSide(color: colorScheme.outline),
          padding: const EdgeInsets.symmetric(
            horizontal: AppSpacing.large,
            vertical: AppSpacing.medium,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppSpacing.radiusMedium),
          ),
          textStyle: _getTextTheme(colorScheme).labelLarge?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      
      // Text Button Theme
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: colorScheme.primary,
          padding: const EdgeInsets.symmetric(
            horizontal: AppSpacing.medium,
            vertical: AppSpacing.small,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppSpacing.radiusSmall),
          ),
          textStyle: _getTextTheme(colorScheme).labelLarge?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      
      // Input Decoration Theme
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppSpacing.radiusMedium),
          borderSide: BorderSide(color: colorScheme.outline),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppSpacing.radiusMedium),
          borderSide: BorderSide(color: colorScheme.outline),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppSpacing.radiusMedium),
          borderSide: BorderSide(color: colorScheme.primary, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppSpacing.radiusMedium),
          borderSide: BorderSide(color: colorScheme.error),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppSpacing.radiusMedium),
          borderSide: BorderSide(color: colorScheme.error, width: 2),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: AppSpacing.medium,
          vertical: AppSpacing.medium,
        ),
        labelStyle: _getTextTheme(colorScheme).bodyMedium?.copyWith(
          color: colorScheme.onSurfaceVariant,
        ),
        hintStyle: _getTextTheme(colorScheme).bodyMedium?.copyWith(
          color: colorScheme.onSurfaceVariant.withValues(alpha: 0.6),
        ),
      ),
      
      // Floating Action Button Theme
      floatingActionButtonTheme: FloatingActionButtonThemeData(
        backgroundColor: colorScheme.primary,
        foregroundColor: colorScheme.onPrimary,
        elevation: 4,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppSpacing.radiusLarge),
        ),
      ),
      
      // Bottom Navigation Bar Theme
      bottomNavigationBarTheme: BottomNavigationBarThemeData(
        backgroundColor: colorScheme.surface,
        selectedItemColor: colorScheme.primary,
        unselectedItemColor: colorScheme.onSurfaceVariant,
        type: BottomNavigationBarType.fixed,
        elevation: 8,
        selectedLabelStyle: _getTextTheme(colorScheme).labelSmall?.copyWith(
          fontWeight: FontWeight.w600,
        ),
        unselectedLabelStyle: _getTextTheme(colorScheme).labelSmall,
      ),
      
      // Navigation Rail Theme
      navigationRailTheme: NavigationRailThemeData(
        backgroundColor: colorScheme.surface,
        selectedIconTheme: IconThemeData(color: colorScheme.primary),
        unselectedIconTheme: IconThemeData(color: colorScheme.onSurfaceVariant),
        selectedLabelTextStyle: _getTextTheme(colorScheme).labelMedium?.copyWith(
          color: colorScheme.primary,
          fontWeight: FontWeight.w600,
        ),
        unselectedLabelTextStyle: _getTextTheme(colorScheme).labelMedium?.copyWith(
          color: colorScheme.onSurfaceVariant,
        ),
      ),
      
      // Chip Theme
      chipTheme: ChipThemeData(
        backgroundColor: colorScheme.surfaceContainerHighest,
        selectedColor: colorScheme.secondaryContainer,
        disabledColor: colorScheme.onSurface.withValues(alpha: 0.12),
        labelStyle: _getTextTheme(colorScheme).labelMedium,
        secondaryLabelStyle: _getTextTheme(colorScheme).labelMedium?.copyWith(
          color: colorScheme.onSecondaryContainer,
        ),
        padding: const EdgeInsets.symmetric(
          horizontal: AppSpacing.small,
          vertical: AppSpacing.extraSmall,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppSpacing.radiusSmall),
        ),
      ),
      
      // Dialog Theme
      dialogTheme: DialogTheme(
        backgroundColor: colorScheme.surface,
        surfaceTintColor: colorScheme.surfaceTint,
        elevation: 6,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppSpacing.radiusLarge),
        ),
        titleTextStyle: _getTextTheme(colorScheme).headlineSmall?.copyWith(
          color: colorScheme.onSurface,
          fontWeight: FontWeight.w600,
        ),
        contentTextStyle: _getTextTheme(colorScheme).bodyMedium?.copyWith(
          color: colorScheme.onSurfaceVariant,
        ),
      ),
      
      // Snack Bar Theme
      snackBarTheme: SnackBarThemeData(
        backgroundColor: colorScheme.inverseSurface,
        contentTextStyle: _getTextTheme(colorScheme).bodyMedium?.copyWith(
          color: colorScheme.onInverseSurface,
        ),
        actionTextColor: colorScheme.inversePrimary,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppSpacing.radiusSmall),
        ),
      ),
      
      // Progress Indicator Theme
      progressIndicatorTheme: ProgressIndicatorThemeData(
        color: colorScheme.primary,
        linearTrackColor: colorScheme.surfaceContainerHighest,
        circularTrackColor: colorScheme.surfaceContainerHighest,
      ),
      
      // Switch Theme
      switchTheme: SwitchThemeData(
        thumbColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return colorScheme.onPrimary;
          }
          return colorScheme.outline;
        }),
        trackColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return colorScheme.primary;
          }
          return colorScheme.surfaceContainerHighest;
        }),
      ),
      
      // Checkbox Theme
      checkboxTheme: CheckboxThemeData(
        fillColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return colorScheme.primary;
          }
          return Colors.transparent;
        }),
        checkColor: WidgetStateProperty.all(colorScheme.onPrimary),
        side: BorderSide(color: colorScheme.outline),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppSpacing.radiusExtraSmall),
        ),
      ),
      
      // Radio Theme
      radioTheme: RadioThemeData(
        fillColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return colorScheme.primary;
          }
          return colorScheme.outline;
        }),
      ),
      
      // Divider Theme
      dividerTheme: DividerThemeData(
        color: colorScheme.outlineVariant,
        thickness: 1,
        space: 1,
      ),
      
      // List Tile Theme
      listTileTheme: ListTileThemeData(
        contentPadding: const EdgeInsets.symmetric(
          horizontal: AppSpacing.medium,
          vertical: AppSpacing.extraSmall,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppSpacing.radiusSmall),
        ),
        titleTextStyle: _getTextTheme(colorScheme).bodyLarge?.copyWith(
          color: colorScheme.onSurface,
          fontWeight: FontWeight.w500,
        ),
        subtitleTextStyle: _getTextTheme(colorScheme).bodyMedium?.copyWith(
          color: colorScheme.onSurfaceVariant,
        ),
        leadingAndTrailingTextStyle: _getTextTheme(colorScheme).labelMedium?.copyWith(
          color: colorScheme.onSurfaceVariant,
        ),
      ),
    );
  }

  /// Get dark theme data
  ThemeData get darkTheme {
    final colorScheme = _getColorScheme(true);
    
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      colorScheme: colorScheme,
      
      // Typography
      textTheme: _getTextTheme(colorScheme),
      
      // App Bar Theme
      appBarTheme: AppBarTheme(
        backgroundColor: colorScheme.surface,
        foregroundColor: colorScheme.onSurface,
        elevation: 0,
        centerTitle: true,
        titleTextStyle: _getTextTheme(colorScheme).titleLarge?.copyWith(
          fontWeight: FontWeight.w600,
          color: colorScheme.onSurface,
        ),
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.light,
          statusBarBrightness: Brightness.dark,
        ),
      ),
      
      // Card Theme
      cardTheme: CardTheme(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppSpacing.radiusLarge),
        ),
        color: colorScheme.surface,
        surfaceTintColor: colorScheme.surfaceTint,
      ),
      
      // Elevated Button Theme
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: colorScheme.primary,
          foregroundColor: colorScheme.onPrimary,
          elevation: 2,
          padding: const EdgeInsets.symmetric(
            horizontal: AppSpacing.large,
            vertical: AppSpacing.medium,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppSpacing.radiusMedium),
          ),
          textStyle: _getTextTheme(colorScheme).labelLarge?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      
      // Outlined Button Theme
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: colorScheme.primary,
          side: BorderSide(color: colorScheme.outline),
          padding: const EdgeInsets.symmetric(
            horizontal: AppSpacing.large,
            vertical: AppSpacing.medium,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppSpacing.radiusMedium),
          ),
          textStyle: _getTextTheme(colorScheme).labelLarge?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      
      // Text Button Theme
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: colorScheme.primary,
          padding: const EdgeInsets.symmetric(
            horizontal: AppSpacing.medium,
            vertical: AppSpacing.small,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppSpacing.radiusSmall),
          ),
          textStyle: _getTextTheme(colorScheme).labelLarge?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      
      // Input Decoration Theme
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppSpacing.radiusMedium),
          borderSide: BorderSide(color: colorScheme.outline),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppSpacing.radiusMedium),
          borderSide: BorderSide(color: colorScheme.outline),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppSpacing.radiusMedium),
          borderSide: BorderSide(color: colorScheme.primary, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppSpacing.radiusMedium),
          borderSide: BorderSide(color: colorScheme.error),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppSpacing.radiusMedium),
          borderSide: BorderSide(color: colorScheme.error, width: 2),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: AppSpacing.medium,
          vertical: AppSpacing.medium,
        ),
        labelStyle: _getTextTheme(colorScheme).bodyMedium?.copyWith(
          color: colorScheme.onSurfaceVariant,
        ),
        hintStyle: _getTextTheme(colorScheme).bodyMedium?.copyWith(
          color: colorScheme.onSurfaceVariant.withValues(alpha: 0.6),
        ),
      ),
      
      // Floating Action Button Theme
      floatingActionButtonTheme: FloatingActionButtonThemeData(
        backgroundColor: colorScheme.primary,
        foregroundColor: colorScheme.onPrimary,
        elevation: 4,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppSpacing.radiusLarge),
        ),
      ),
      
      // Bottom Navigation Bar Theme
      bottomNavigationBarTheme: BottomNavigationBarThemeData(
        backgroundColor: colorScheme.surface,
        selectedItemColor: colorScheme.primary,
        unselectedItemColor: colorScheme.onSurfaceVariant,
        type: BottomNavigationBarType.fixed,
        elevation: 8,
        selectedLabelStyle: _getTextTheme(colorScheme).labelSmall?.copyWith(
          fontWeight: FontWeight.w600,
        ),
        unselectedLabelStyle: _getTextTheme(colorScheme).labelSmall,
      ),
      
      // Navigation Rail Theme
      navigationRailTheme: NavigationRailThemeData(
        backgroundColor: colorScheme.surface,
        selectedIconTheme: IconThemeData(color: colorScheme.primary),
        unselectedIconTheme: IconThemeData(color: colorScheme.onSurfaceVariant),
        selectedLabelTextStyle: _getTextTheme(colorScheme).labelMedium?.copyWith(
          color: colorScheme.primary,
          fontWeight: FontWeight.w600,
        ),
        unselectedLabelTextStyle: _getTextTheme(colorScheme).labelMedium?.copyWith(
          color: colorScheme.onSurfaceVariant,
        ),
      ),
      
      // Chip Theme
      chipTheme: ChipThemeData(
        backgroundColor: colorScheme.surfaceContainerHighest,
        selectedColor: colorScheme.secondaryContainer,
        disabledColor: colorScheme.onSurface.withValues(alpha: 0.12),
        labelStyle: _getTextTheme(colorScheme).labelMedium,
        secondaryLabelStyle: _getTextTheme(colorScheme).labelMedium?.copyWith(
          color: colorScheme.onSecondaryContainer,
        ),
        padding: const EdgeInsets.symmetric(
          horizontal: AppSpacing.small,
          vertical: AppSpacing.extraSmall,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppSpacing.radiusSmall),
        ),
      ),
      
      // Dialog Theme
      dialogTheme: DialogTheme(
        backgroundColor: colorScheme.surface,
        surfaceTintColor: colorScheme.surfaceTint,
        elevation: 6,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppSpacing.radiusLarge),
        ),
        titleTextStyle: _getTextTheme(colorScheme).headlineSmall?.copyWith(
          color: colorScheme.onSurface,
          fontWeight: FontWeight.w600,
        ),
        contentTextStyle: _getTextTheme(colorScheme).bodyMedium?.copyWith(
          color: colorScheme.onSurfaceVariant,
        ),
      ),
      
      // Snack Bar Theme
      snackBarTheme: SnackBarThemeData(
        backgroundColor: colorScheme.inverseSurface,
        contentTextStyle: _getTextTheme(colorScheme).bodyMedium?.copyWith(
          color: colorScheme.onInverseSurface,
        ),
        actionTextColor: colorScheme.inversePrimary,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppSpacing.radiusSmall),
        ),
      ),
      
      // Progress Indicator Theme
      progressIndicatorTheme: ProgressIndicatorThemeData(
        color: colorScheme.primary,
        linearTrackColor: colorScheme.surfaceContainerHighest,
        circularTrackColor: colorScheme.surfaceContainerHighest,
      ),
      
      // Switch Theme
      switchTheme: SwitchThemeData(
        thumbColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return colorScheme.onPrimary;
          }
          return colorScheme.outline;
        }),
        trackColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return colorScheme.primary;
          }
          return colorScheme.surfaceContainerHighest;
        }),
      ),
      
      // Checkbox Theme
      checkboxTheme: CheckboxThemeData(
        fillColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return colorScheme.primary;
          }
          return Colors.transparent;
        }),
        checkColor: WidgetStateProperty.all(colorScheme.onPrimary),
        side: BorderSide(color: colorScheme.outline),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppSpacing.radiusExtraSmall),
        ),
      ),
      
      // Radio Theme
      radioTheme: RadioThemeData(
        fillColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return colorScheme.primary;
          }
          return colorScheme.outline;
        }),
      ),
      
      // Divider Theme
      dividerTheme: DividerThemeData(
        color: colorScheme.outlineVariant,
        thickness: 1,
        space: 1,
      ),
      
      // List Tile Theme
      listTileTheme: ListTileThemeData(
        contentPadding: const EdgeInsets.symmetric(
          horizontal: AppSpacing.medium,
          vertical: AppSpacing.extraSmall,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppSpacing.radiusSmall),
        ),
        titleTextStyle: _getTextTheme(colorScheme).bodyLarge?.copyWith(
          color: colorScheme.onSurface,
          fontWeight: FontWeight.w500,
        ),
        subtitleTextStyle: _getTextTheme(colorScheme).bodyMedium?.copyWith(
          color: colorScheme.onSurfaceVariant,
        ),
        leadingAndTrailingTextStyle: _getTextTheme(colorScheme).labelMedium?.copyWith(
          color: colorScheme.onSurfaceVariant,
        ),
      ),
    );
  }

  /// Get color scheme based on current selection and brightness
  ColorScheme _getColorScheme(bool isDark) {
    switch (_currentColorScheme) {
      case AppColorScheme.blue:
        return isDark ? _blueDarkColorScheme : _blueLightColorScheme;
      case AppColorScheme.green:
        return isDark ? _greenDarkColorScheme : _greenLightColorScheme;
      case AppColorScheme.purple:
        return isDark ? _purpleDarkColorScheme : _purpleLightColorScheme;
      case AppColorScheme.orange:
        return isDark ? _orangeDarkColorScheme : _orangeLightColorScheme;
      case AppColorScheme.teal:
        return isDark ? _tealDarkColorScheme : _tealLightColorScheme;
    }
  }

  /// Get text theme for the app with Google Fonts support
  TextTheme _getTextTheme(ColorScheme colorScheme) {
    // Use Kantumruy for Khmer support, fallback to Inter for other languages
    return GoogleFonts.kantumruyProTextTheme().copyWith(
      // Display styles
      displayLarge: GoogleFonts.kantumruyPro(
        fontSize: 57,
        fontWeight: FontWeight.w400,
        letterSpacing: -0.25,
        color: colorScheme.onSurface,
        height: 1.12,
      ),
      displayMedium: GoogleFonts.kantumruyPro(
        fontSize: 45,
        fontWeight: FontWeight.w400,
        letterSpacing: 0,
        color: colorScheme.onSurface,
        height: 1.16,
      ),
      displaySmall: GoogleFonts.kantumruyPro(
        fontSize: 36,
        fontWeight: FontWeight.w400,
        letterSpacing: 0,
        color: colorScheme.onSurface,
        height: 1.22,
      ),
      
      // Headline styles
      headlineLarge: GoogleFonts.kantumruyPro(
        fontSize: 32,
        fontWeight: FontWeight.w400,
        letterSpacing: 0,
        color: colorScheme.onSurface,
        height: 1.25,
      ),
      headlineMedium: GoogleFonts.kantumruyPro(
        fontSize: 28,
        fontWeight: FontWeight.w400,
        letterSpacing: 0,
        color: colorScheme.onSurface,
        height: 1.29,
      ),
      headlineSmall: GoogleFonts.kantumruyPro(
        fontSize: 24,
        fontWeight: FontWeight.w400,
        letterSpacing: 0,
        color: colorScheme.onSurface,
        height: 1.33,
      ),
      
      // Title styles
      titleLarge: GoogleFonts.kantumruyPro(
        fontSize: 22,
        fontWeight: FontWeight.w400,
        letterSpacing: 0,
        color: colorScheme.onSurface,
        height: 1.27,
      ),
      titleMedium: GoogleFonts.kantumruyPro(
        fontSize: 16,
        fontWeight: FontWeight.w500,
        letterSpacing: 0.15,
        color: colorScheme.onSurface,
        height: 1.50,
      ),
      titleSmall: GoogleFonts.kantumruyPro(
        fontSize: 14,
        fontWeight: FontWeight.w500,
        letterSpacing: 0.1,
        color: colorScheme.onSurface,
        height: 1.43,
      ),
      
      // Label styles
      labelLarge: GoogleFonts.kantumruyPro(
        fontSize: 14,
        fontWeight: FontWeight.w500,
        letterSpacing: 0.1,
        color: colorScheme.onSurface,
        height: 1.43,
      ),
      labelMedium: GoogleFonts.kantumruyPro(
        fontSize: 12,
        fontWeight: FontWeight.w500,
        letterSpacing: 0.5,
        color: colorScheme.onSurface,
        height: 1.33,
      ),
      labelSmall: GoogleFonts.kantumruyPro(
        fontSize: 11,
        fontWeight: FontWeight.w500,
        letterSpacing: 0.5,
        color: colorScheme.onSurface,
        height: 1.45,
      ),
      
      // Body styles
      bodyLarge: GoogleFonts.kantumruyPro(
        fontSize: 16,
        fontWeight: FontWeight.w400,
        letterSpacing: 0.5,
        color: colorScheme.onSurface,
        height: 1.50,
      ),
      bodyMedium: GoogleFonts.kantumruyPro(
        fontSize: 14,
        fontWeight: FontWeight.w400,
        letterSpacing: 0.25,
        color: colorScheme.onSurface,
        height: 1.43,
      ),
      bodySmall: GoogleFonts.kantumruyPro(
        fontSize: 12,
        fontWeight: FontWeight.w400,
        letterSpacing: 0.4,
        color: colorScheme.onSurfaceVariant,
        height: 1.33,
      ),
    );
  }

  /// Blue color schemes
  static const ColorScheme _blueLightColorScheme = ColorScheme(
    brightness: Brightness.light,
    primary: Color(0xFF1976D2),
    onPrimary: Color(0xFFFFFFFF),
    primaryContainer: Color(0xFFBBDEFB),
    onPrimaryContainer: Color(0xFF0D47A1),
    secondary: Color(0xFF03DAC6),
    onSecondary: Color(0xFF000000),
    secondaryContainer: Color(0xFFB2DFDB),
    onSecondaryContainer: Color(0xFF004D40),
    tertiary: Color(0xFF9C27B0),
    onTertiary: Color(0xFFFFFFFF),
    tertiaryContainer: Color(0xFFE1BEE7),
    onTertiaryContainer: Color(0xFF4A148C),
    error: Color(0xFFD32F2F),
    onError: Color(0xFFFFFFFF),
    errorContainer: Color(0xFFFFCDD2),
    onErrorContainer: Color(0xFFB71C1C),
    surface: Color(0xFFFFFFFF),
    onSurface: Color(0xFF1C1B1F),
    surfaceContainerHighest: Color(0xFFE3F2FD),
    onSurfaceVariant: Color(0xFF424242),
    outline: Color(0xFF757575),
    outlineVariant: Color(0xFFBDBDBD),
    shadow: Color(0xFF000000),
    scrim: Color(0xFF000000),
    inverseSurface: Color(0xFF313033),
    onInverseSurface: Color(0xFFF4EFF4),
    inversePrimary: Color(0xFF90CAF9),
    surfaceTint: Color(0xFF1976D2),
  );

  static const ColorScheme _blueDarkColorScheme = ColorScheme(
    brightness: Brightness.dark,
    primary: Color(0xFF90CAF9),
    onPrimary: Color(0xFF0D47A1),
    primaryContainer: Color(0xFF1565C0),
    onPrimaryContainer: Color(0xFFE3F2FD),
    secondary: Color(0xFF80CBC4),
    onSecondary: Color(0xFF004D40),
    secondaryContainer: Color(0xFF00695C),
    onSecondaryContainer: Color(0xFFB2DFDB),
    tertiary: Color(0xFFCE93D8),
    onTertiary: Color(0xFF4A148C),
    tertiaryContainer: Color(0xFF7B1FA2),
    onTertiaryContainer: Color(0xFFE1BEE7),
    error: Color(0xFFEF5350),
    onError: Color(0xFFB71C1C),
    errorContainer: Color(0xFFC62828),
    onErrorContainer: Color(0xFFFFCDD2),
    surface: Color(0xFF1E1E1E),
    onSurface: Color(0xFFE6E1E5),
    surfaceContainerHighest: Color(0xFF263238),
    onSurfaceVariant: Color(0xFFB0BEC5),
    outline: Color(0xFF78909C),
    outlineVariant: Color(0xFF455A64),
    shadow: Color(0xFF000000),
    scrim: Color(0xFF000000),
    inverseSurface: Color(0xFFE6E1E5),
    onInverseSurface: Color(0xFF313033),
    inversePrimary: Color(0xFF1976D2),
    surfaceTint: Color(0xFF90CAF9),
  );

  /// Green color schemes
  static const ColorScheme _greenLightColorScheme = ColorScheme(
    brightness: Brightness.light,
    primary: Color(0xFF388E3C),
    onPrimary: Color(0xFFFFFFFF),
    primaryContainer: Color(0xFFC8E6C9),
    onPrimaryContainer: Color(0xFF1B5E20),
    secondary: Color(0xFF66BB6A),
    onSecondary: Color(0xFF000000),
    secondaryContainer: Color(0xFFE8F5E8),
    onSecondaryContainer: Color(0xFF2E7D32),
    tertiary: Color(0xFF8BC34A),
    onTertiary: Color(0xFF000000),
    tertiaryContainer: Color(0xFFDCEDC8),
    onTertiaryContainer: Color(0xFF33691E),
    error: Color(0xFFD32F2F),
    onError: Color(0xFFFFFFFF),
    errorContainer: Color(0xFFFFCDD2),
    onErrorContainer: Color(0xFFB71C1C),
    surface: Color(0xFFFFFFFF),
    onSurface: Color(0xFF1C1B1F),
    surfaceContainerHighest: Color(0xFFE8F5E8),
    onSurfaceVariant: Color(0xFF424242),
    outline: Color(0xFF757575),
    outlineVariant: Color(0xFFBDBDBD),
    shadow: Color(0xFF000000),
    scrim: Color(0xFF000000),
    inverseSurface: Color(0xFF313033),
    onInverseSurface: Color(0xFFF4EFF4),
    inversePrimary: Color(0xFFA5D6A7),
    surfaceTint: Color(0xFF388E3C),
  );

  static const ColorScheme _greenDarkColorScheme = ColorScheme(
    brightness: Brightness.dark,
    primary: Color(0xFFA5D6A7),
    onPrimary: Color(0xFF1B5E20),
    primaryContainer: Color(0xFF2E7D32),
    onPrimaryContainer: Color(0xFFC8E6C9),
    secondary: Color(0xFF81C784),
    onSecondary: Color(0xFF2E7D32),
    secondaryContainer: Color(0xFF388E3C),
    onSecondaryContainer: Color(0xFFE8F5E8),
    tertiary: Color(0xFFAED581),
    onTertiary: Color(0xFF33691E),
    tertiaryContainer: Color(0xFF689F38),
    onTertiaryContainer: Color(0xFFDCEDC8),
    error: Color(0xFFEF5350),
    onError: Color(0xFFB71C1C),
    errorContainer: Color(0xFFC62828),
    onErrorContainer: Color(0xFFFFCDD2),
    surface: Color(0xFF1E1E1E),
    onSurface: Color(0xFFE6E1E5),
    surfaceContainerHighest: Color(0xFF2E2E2E),
    onSurfaceVariant: Color(0xFFB0BEC5),
    outline: Color(0xFF78909C),
    outlineVariant: Color(0xFF455A64),
    shadow: Color(0xFF000000),
    scrim: Color(0xFF000000),
    inverseSurface: Color(0xFFE6E1E5),
    onInverseSurface: Color(0xFF313033),
    inversePrimary: Color(0xFF388E3C),
    surfaceTint: Color(0xFFA5D6A7),
  );

  /// Purple color schemes
  static const ColorScheme _purpleLightColorScheme = ColorScheme(
    brightness: Brightness.light,
    primary: Color(0xFF7B1FA2),
    onPrimary: Color(0xFFFFFFFF),
    primaryContainer: Color(0xFFE1BEE7),
    onPrimaryContainer: Color(0xFF4A148C),
    secondary: Color(0xFF9C27B0),
    onSecondary: Color(0xFFFFFFFF),
    secondaryContainer: Color(0xFFF3E5F5),
    onSecondaryContainer: Color(0xFF6A1B9A),
    tertiary: Color(0xFF673AB7),
    onTertiary: Color(0xFFFFFFFF),
    tertiaryContainer: Color(0xFFD1C4E9),
    onTertiaryContainer: Color(0xFF311B92),
    error: Color(0xFFD32F2F),
    onError: Color(0xFFFFFFFF),
    errorContainer: Color(0xFFFFCDD2),
    onErrorContainer: Color(0xFFB71C1C),
    surface: Color(0xFFFFFFFF),
    onSurface: Color(0xFF1C1B1F),
    surfaceContainerHighest: Color(0xFFF3E5F5),
    onSurfaceVariant: Color(0xFF424242),
    outline: Color(0xFF757575),
    outlineVariant: Color(0xFFBDBDBD),
    shadow: Color(0xFF000000),
    scrim: Color(0xFF000000),
    inverseSurface: Color(0xFF313033),
    onInverseSurface: Color(0xFFF4EFF4),
    inversePrimary: Color(0xFFCE93D8),
    surfaceTint: Color(0xFF7B1FA2),
  );

  static const ColorScheme _purpleDarkColorScheme = ColorScheme(
    brightness: Brightness.dark,
    primary: Color(0xFFCE93D8),
    onPrimary: Color(0xFF4A148C),
    primaryContainer: Color(0xFF6A1B9A),
    onPrimaryContainer: Color(0xFFE1BEE7),
    secondary: Color(0xFFBA68C8),
    onSecondary: Color(0xFF6A1B9A),
    secondaryContainer: Color(0xFF8E24AA),
    onSecondaryContainer: Color(0xFFF3E5F5),
    tertiary: Color(0xFF9575CD),
    onTertiary: Color(0xFF311B92),
    tertiaryContainer: Color(0xFF512DA8),
    onTertiaryContainer: Color(0xFFD1C4E9),
    error: Color(0xFFEF5350),
    onError: Color(0xFFB71C1C),
    errorContainer: Color(0xFFC62828),
    onErrorContainer: Color(0xFFFFCDD2),
    surface: Color(0xFF1E1E1E),
    onSurface: Color(0xFFE6E1E5),
    surfaceContainerHighest: Color(0xFF2E2E2E),
    onSurfaceVariant: Color(0xFFB0BEC5),
    outline: Color(0xFF78909C),
    outlineVariant: Color(0xFF455A64),
    shadow: Color(0xFF000000),
    scrim: Color(0xFF000000),
    inverseSurface: Color(0xFFE6E1E5),
    onInverseSurface: Color(0xFF313033),
    inversePrimary: Color(0xFF7B1FA2),
    surfaceTint: Color(0xFFCE93D8),
  );

  /// Orange color schemes
  static const ColorScheme _orangeLightColorScheme = ColorScheme(
    brightness: Brightness.light,
    primary: Color(0xFFFF6F00),
    onPrimary: Color(0xFFFFFFFF),
    primaryContainer: Color(0xFFFFE0B2),
    onPrimaryContainer: Color(0xFFE65100),
    secondary: Color(0xFFFF9800),
    onSecondary: Color(0xFF000000),
    secondaryContainer: Color(0xFFFFF3E0),
    onSecondaryContainer: Color(0xFFEF6C00),
    tertiary: Color(0xFFFF5722),
    onTertiary: Color(0xFFFFFFFF),
    tertiaryContainer: Color(0xFFFFCCBC),
    onTertiaryContainer: Color(0xFFBF360C),
    error: Color(0xFFD32F2F),
    onError: Color(0xFFFFFFFF),
    errorContainer: Color(0xFFFFCDD2),
    onErrorContainer: Color(0xFFB71C1C),
    surface: Color(0xFFFFFFFF),
    onSurface: Color(0xFF1C1B1F),
    surfaceContainerHighest: Color(0xFFFFF3E0),
    onSurfaceVariant: Color(0xFF424242),
    outline: Color(0xFF757575),
    outlineVariant: Color(0xFFBDBDBD),
    shadow: Color(0xFF000000),
    scrim: Color(0xFF000000),
    inverseSurface: Color(0xFF313033),
    onInverseSurface: Color(0xFFF4EFF4),
    inversePrimary: Color(0xFFFFB74D),
    surfaceTint: Color(0xFFFF6F00),
  );

  static const ColorScheme _orangeDarkColorScheme = ColorScheme(
    brightness: Brightness.dark,
    primary: Color(0xFFFFB74D),
    onPrimary: Color(0xFFE65100),
    primaryContainer: Color(0xFFEF6C00),
    onPrimaryContainer: Color(0xFFFFE0B2),
    secondary: Color(0xFFFFCC02),
    onSecondary: Color(0xFFEF6C00),
    secondaryContainer: Color(0xFFFF8F00),
    onSecondaryContainer: Color(0xFFFFF3E0),
    tertiary: Color(0xFFFF8A65),
    onTertiary: Color(0xFFBF360C),
    tertiaryContainer: Color(0xFFD84315),
    onTertiaryContainer: Color(0xFFFFCCBC),
    error: Color(0xFFEF5350),
    onError: Color(0xFFB71C1C),
    errorContainer: Color(0xFFC62828),
    onErrorContainer: Color(0xFFFFCDD2),
    surface: Color(0xFF1E1E1E),
    onSurface: Color(0xFFE6E1E5),
    surfaceContainerHighest: Color(0xFF2E2E2E),
    onSurfaceVariant: Color(0xFFB0BEC5),
    outline: Color(0xFF78909C),
    outlineVariant: Color(0xFF455A64),
    shadow: Color(0xFF000000),
    scrim: Color(0xFF000000),
    inverseSurface: Color(0xFFE6E1E5),
    onInverseSurface: Color(0xFF313033),
    inversePrimary: Color(0xFFFF6F00),
    surfaceTint: Color(0xFFFFB74D),
  );

  /// Teal color schemes
  static const ColorScheme _tealLightColorScheme = ColorScheme(
    brightness: Brightness.light,
    primary: Color(0xFF00695C),
    onPrimary: Color(0xFFFFFFFF),
    primaryContainer: Color(0xFFB2DFDB),
    onPrimaryContainer: Color(0xFF004D40),
    secondary: Color(0xFF26A69A),
    onSecondary: Color(0xFF000000),
    secondaryContainer: Color(0xFFE0F2F1),
    onSecondaryContainer: Color(0xFF00695C),
    tertiary: Color(0xFF009688),
    onTertiary: Color(0xFFFFFFFF),
    tertiaryContainer: Color(0xFFB2DFDB),
    onTertiaryContainer: Color(0xFF00695C),
    error: Color(0xFFD32F2F),
    onError: Color(0xFFFFFFFF),
    errorContainer: Color(0xFFFFCDD2),
    onErrorContainer: Color(0xFFB71C1C),
    surface: Color(0xFFFFFFFF),
    onSurface: Color(0xFF1C1B1F),
    surfaceContainerHighest: Color(0xFFE0F2F1),
    onSurfaceVariant: Color(0xFF424242),
    outline: Color(0xFF757575),
    outlineVariant: Color(0xFFBDBDBD),
    shadow: Color(0xFF000000),
    scrim: Color(0xFF000000),
    inverseSurface: Color(0xFF313033),
    onInverseSurface: Color(0xFFF4EFF4),
    inversePrimary: Color(0xFF80CBC4),
    surfaceTint: Color(0xFF00695C),
  );

  static const ColorScheme _tealDarkColorScheme = ColorScheme(
    brightness: Brightness.dark,
    primary: Color(0xFF80CBC4),
    onPrimary: Color(0xFF004D40),
    primaryContainer: Color(0xFF00695C),
    onPrimaryContainer: Color(0xFFB2DFDB),
    secondary: Color(0xFF4DB6AC),
    onSecondary: Color(0xFF00695C),
    secondaryContainer: Color(0xFF00796B),
    onSecondaryContainer: Color(0xFFE0F2F1),
    tertiary: Color(0xFF26A69A),
    onTertiary: Color(0xFF00695C),
    tertiaryContainer: Color(0xFF00796B),
    onTertiaryContainer: Color(0xFFB2DFDB),
    error: Color(0xFFEF5350),
    onError: Color(0xFFB71C1C),
    errorContainer: Color(0xFFC62828),
    onErrorContainer: Color(0xFFFFCDD2),
    surface: Color(0xFF1E1E1E),
    onSurface: Color(0xFFE6E1E5),
    surfaceContainerHighest: Color(0xFF2E2E2E),
    onSurfaceVariant: Color(0xFFB0BEC5),
    outline: Color(0xFF78909C),
    outlineVariant: Color(0xFF455A64),
    shadow: Color(0xFF000000),
    scrim: Color(0xFF000000),
    inverseSurface: Color(0xFFE6E1E5),
    onInverseSurface: Color(0xFF313033),
    inversePrimary: Color(0xFF00695C),
    surfaceTint: Color(0xFF80CBC4),
  );

  /// Check if service is initialized
  bool get isInitialized => _isInitialized;
}

/// Available color schemes
enum AppColorScheme {
  blue,
  green,
  purple,
  orange,
  teal,
}

/// App spacing constants
class AppSpacing {
  static const double extraSmall = 4.0;
  static const double small = 8.0;
  static const double medium = 16.0;
  static const double large = 24.0;
  static const double extraLarge = 32.0;
  static const double huge = 48.0;
  
  // Border radius
  static const double radiusExtraSmall = 4.0;
  static const double radiusSmall = 8.0;
  static const double radiusMedium = 12.0;
  static const double radiusLarge = 16.0;
  static const double radiusExtraLarge = 24.0;
  
  // Elevation
  static const double elevationLow = 1.0;
  static const double elevationMedium = 3.0;
  static const double elevationHigh = 6.0;
  static const double elevationVeryHigh = 12.0;
}

/// App typography constants
class AppTypography {
  // Font family - Using Google Fonts Kantumruy Pro for Khmer support
  static String get fontFamily => GoogleFonts.kantumruyPro().fontFamily ?? 'Kantumruy Pro';
  
  // Font weights
  static const FontWeight light = FontWeight.w300;
  static const FontWeight regular = FontWeight.w400;
  static const FontWeight medium = FontWeight.w500;
  static const FontWeight semiBold = FontWeight.w600;
  static const FontWeight bold = FontWeight.w700;
  
  // Letter spacing
  static const double letterSpacingTight = -0.25;
  static const double letterSpacingNormal = 0.0;
  static const double letterSpacingWide = 0.5;
  static const double letterSpacingExtraWide = 1.0;
}

/// App animation constants
class AppAnimations {
  static const Duration fast = Duration(milliseconds: 150);
  static const Duration normal = Duration(milliseconds: 300);
  static const Duration slow = Duration(milliseconds: 500);
  static const Duration verySlow = Duration(milliseconds: 800);
  
  // Curves
  static const Curve easeIn = Curves.easeIn;
  static const Curve easeOut = Curves.easeOut;
  static const Curve easeInOut = Curves.easeInOut;
  static const Curve bounceIn = Curves.bounceIn;
  static const Curve bounceOut = Curves.bounceOut;
  static const Curve elasticIn = Curves.elasticIn;
  static const Curve elasticOut = Curves.elasticOut;
}

/// App breakpoints for responsive design
class AppBreakpoints {
  static const double mobile = 600;
  static const double tablet = 900;
  static const double desktop = 1200;
  static const double largeDesktop = 1600;
}