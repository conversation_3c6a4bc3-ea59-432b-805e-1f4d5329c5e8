import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/loan_application.dart' as models;
import '../services/base_loan_service.dart';
import '../services/loan_application_service.dart';
import '../services/mock_loan_service.dart';
import '../main.dart';

/// Provider for the loan application service
final loanApplicationServiceProvider = Provider<BaseLoanService>((ref) {
  return AppConfig.useMockData 
      ? MockLoanApplicationService() 
      : LoanApplicationService();
});

/// State class for loan applications list
class LoanApplicationsState {
  final List<models.LoanApplication> applications;
  final bool isLoading;
  final String? error;
  final bool hasMore;
  final int currentPage;

  const LoanApplicationsState({
    this.applications = const [],
    this.isLoading = false,
    this.error,
    this.hasMore = true,
    this.currentPage = 1,
  });

  LoanApplicationsState copyWith({
    List<models.LoanApplication>? applications,
    bool? isLoading,
    String? error,
    bool? hasMore,
    int? currentPage,
  }) {
    return LoanApplicationsState(
      applications: applications ?? this.applications,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      hasMore: hasMore ?? this.hasMore,
      currentPage: currentPage ?? this.currentPage,
    );
  }
}

/// Notifier for managing loan applications state
class LoanApplicationsNotifier extends StateNotifier<LoanApplicationsState> {
  final BaseLoanService _service;

  LoanApplicationsNotifier(this._service)
      : super(const LoanApplicationsState());

  /// Fetch applications with optional filters
  Future<void> fetchApplications({
    models.LoanStatus? status,
    String? portfolioOfficerId,
    DateTime? fromDate,
    DateTime? toDate,
    bool refresh = false,
  }) async {
    if (refresh) {
      state = state.copyWith(
        applications: [],
        currentPage: 1,
        hasMore: true,
        error: null,
      );
    }

    if (state.isLoading || !state.hasMore) return;

    state = state.copyWith(isLoading: true, error: null);

    try {
      final applications = await _service.fetchApplications(
        status: status,
        portfolioOfficerId: portfolioOfficerId,
        fromDate: fromDate,
        toDate: toDate,
        page: state.currentPage,
        limit: 20,
      );

      final updatedApplications =
          refresh ? applications : [...state.applications, ...applications];

      state = state.copyWith(
        applications: updatedApplications,
        isLoading: false,
        hasMore: applications.length == 20,
        currentPage: state.currentPage + 1,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// Update application status
  Future<void> updateApplicationStatus(
    String applicationId,
    models.LoanStatus newStatus, {
    String? reason,
    String? approverId,
    String? comments,
  }) async {
    try {
      models.LoanApplication updatedApplication;

      switch (newStatus) {
        case models.LoanStatus.approved:
          updatedApplication = await _service.approveApplication(
            applicationId,
            approverId!,
            comments,
          );
          break;
        case models.LoanStatus.rejected:
          updatedApplication = await _service.rejectApplication(
            applicationId,
            reason!,
          );
          break;
        case models.LoanStatus.disbursed:
          // This would need additional parameters for disbursement
          throw UnimplementedError(
              'Disbursement requires additional parameters');
        default:
          throw ArgumentError('Invalid status update: $newStatus');
      }

      // Update the application in the list
      final updatedApplications = state.applications.map((app) {
        return app.id == applicationId ? updatedApplication : app;
      }).toList();

      state = state.copyWith(applications: updatedApplications);
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  /// Add new application to the list
  void addApplication(models.LoanApplication application) {
    state = state.copyWith(
      applications: [application, ...state.applications],
    );
  }

  /// Remove application from the list
  void removeApplication(String applicationId) {
    final updatedApplications =
        state.applications.where((app) => app.id != applicationId).toList();
    state = state.copyWith(applications: updatedApplications);
  }

  /// Clear error state
  void clearError() {
    state = state.copyWith(error: null);
  }
}

/// Provider for loan applications state
final loanApplicationsProvider =
    StateNotifierProvider<LoanApplicationsNotifier, LoanApplicationsState>(
        (ref) {
  final service = ref.watch(loanApplicationServiceProvider);
  return LoanApplicationsNotifier(service);
});

/// Provider for filtered applications by status
final applicationsByStatusProvider =
    Provider.family<List<models.LoanApplication>, models.LoanStatus?>((ref, status) {
  final applicationsState = ref.watch(loanApplicationsProvider);

  if (status == null) {
    return applicationsState.applications;
  }

  return applicationsState.applications
      .where((app) => app.status == status)
      .toList();
});

/// Provider for application statistics
final applicationStatisticsProvider =
    FutureProvider<ApplicationStatistics>((ref) async {
  final service = ref.watch(loanApplicationServiceProvider);
  return service.getApplicationStatistics();
});

/// Provider for a single application by ID
final loanApplicationProvider =
    FutureProvider.family<models.LoanApplication, String>((ref, id) async {
  final service = ref.watch(loanApplicationServiceProvider);
  return service.fetchApplicationById(id);
});

/// State class for new application form
class NewApplicationState {
  final models.LoanApplication? application;
  final int currentStep;
  final bool isLoading;
  final bool isSubmitting;
  final String? error;
  final Map<String, String> fieldErrors;
  final bool isValid;

  const NewApplicationState({
    this.application,
    this.currentStep = 0,
    this.isLoading = false,
    this.isSubmitting = false,
    this.error,
    this.fieldErrors = const {},
    this.isValid = false,
  });

  NewApplicationState copyWith({
    models.LoanApplication? application,
    int? currentStep,
    bool? isLoading,
    bool? isSubmitting,
    String? error,
    Map<String, String>? fieldErrors,
    bool? isValid,
  }) {
    return NewApplicationState(
      application: application ?? this.application,
      currentStep: currentStep ?? this.currentStep,
      isLoading: isLoading ?? this.isLoading,
      isSubmitting: isSubmitting ?? this.isSubmitting,
      error: error,
      fieldErrors: fieldErrors ?? this.fieldErrors,
      isValid: isValid ?? this.isValid,
    );
  }
}

/// Notifier for managing new application state
class NewApplicationNotifier extends StateNotifier<NewApplicationState> {
  final BaseLoanService _service;

  NewApplicationNotifier(this._service) : super(
    NewApplicationState(
      application: models.LoanApplication(
        id: '',
        status: models.LoanStatus.pending,
        applicationDate: DateTime.now(),
        requestedAmount: 0,
        serviceFee: 0,
        loanTerm: 12,
        agreementUrl: '',
        createdAt: DateTime.now(),
        documents: {},
        applicant: models.Applicant(
          name: '',
          address: '',
          nationalId: '',
          dateOfBirth: DateTime.now(),
          gender: '',
          maritalStatus: '',
          education: '',
          occupation: '',
          phoneNumber: '',
          nid: '',
          email: '',
          profileImageUrl: '',
          monthlyIncome: 0,
        ),
        business: models.Business(
          name: '',
          type: '',
          yearsInOperation: 0,
          monthlyIncome: 0,
          address: '',
          location: '',
          description: '',
        ),
        family: models.Family(
          maritalStatus: '',
          numberOfChildren: 0,
          dependents: 0,
          familyIncome: 0,
          photoUrls: [],
        ),
        assets: models.Assets(
          housePhotoUrl: '',
          otherAssetsPhotoUrl: '',
          estimatedValue: 0,
          ownershipType: 'owned',
          additionalAssets: [],
        ),
        guarantor: models.Guarantor(
          id: '',
          name: '',
          nid: '',
          phoneNumber: '',
          address: '',
          relationship: '',
          occupation: '',
          monthlyIncome: 0,
          collateralPhotoUrl: '',
          collateralValue: 0,
        ),
        groupMembers: [],
        portfolioOfficer: models.PortfolioOfficer(
          id: '',
          name: '',
          email: '',
          phone: '',
        ),
      ),
    ),
  );

  /// Update applicant information
  void updateApplicant(models.Applicant applicant) {
    state = state.copyWith(
      application: state.application?.copyWith(applicant: applicant),
    );
    _validateCurrentStep();
  }

  /// Update business information
  void updateBusiness(models.Business business) {
    state = state.copyWith(
      application: state.application?.copyWith(business: business),
    );
    _validateCurrentStep();
  }

  /// Update family information
  void updateFamily(models.Family family) {
    state = state.copyWith(
      application: state.application?.copyWith(family: family),
    );
    _validateCurrentStep();
  }

  /// Update assets information
  void updateAssets(models.Assets assets) {
    state = state.copyWith(
      application: state.application?.copyWith(assets: assets),
    );
    _validateCurrentStep();
  }

  /// Update guarantor information
  void updateGuarantor(models.Guarantor guarantor) {
    state = state.copyWith(
      application: state.application?.copyWith(guarantor: guarantor),
    );
    _validateCurrentStep();
  }

  /// Update loan details
  void updateLoanDetails({
    double? requestedAmount,
    int? loanTerm,
    double? serviceFee,
  }) {
    state = state.copyWith(
      application: state.application?.copyWith(
        requestedAmount: requestedAmount ?? state.application?.requestedAmount,
        loanTerm: loanTerm ?? state.application?.loanTerm,
        serviceFee: serviceFee ?? state.application?.serviceFee,
      ),
    );
    _validateCurrentStep();
  }

  /// Move to next step
  void nextStep() {
    if (state.currentStep < 6) {
      state = state.copyWith(currentStep: state.currentStep + 1);
    }
  }

  /// Move to previous step
  void previousStep() {
    if (state.currentStep > 0) {
      state = state.copyWith(currentStep: state.currentStep - 1);
    }
  }

  /// Go to specific step
  void goToStep(int step) {
    if (step >= 0 && step <= 6) {
      state = state.copyWith(currentStep: step);
    }
  }

  /// Submit application
  Future<bool> submitApplication() async {
    if (state.application == null) return false;
    
    state = state.copyWith(isSubmitting: true, error: null);

    try {
      final createdApplication =
          await _service.createApplication(state.application!);

      // Reset form after successful submission
      state = NewApplicationState(
        application: models.LoanApplication(
          id: '',
          status: models.LoanStatus.pending,
          applicationDate: DateTime.now(),
          requestedAmount: 0,
          serviceFee: 0,
          loanTerm: 12,
          agreementUrl: '',
          createdAt: DateTime.now(),
          documents: {},
          applicant: models.Applicant(
            name: '',
            address: '',
            nationalId: '',
            dateOfBirth: DateTime.now(),
            gender: '',
            maritalStatus: '',
            education: '',
            occupation: '',
            phoneNumber: '',
            nid: '',
            email: '',
            profileImageUrl: '',
            monthlyIncome: 0,
          ),
          business: models.Business(
            name: '',
            type: '',
            yearsInOperation: 0,
            monthlyIncome: 0,
            address: '',
            location: '',
            description: '',
          ),
          family: models.Family(
            maritalStatus: '',
            numberOfChildren: 0,
            dependents: 0,
            familyIncome: 0,
            photoUrls: [],
          ),
          assets: models.Assets(
            housePhotoUrl: '',
            otherAssetsPhotoUrl: '',
            estimatedValue: 0,
            ownershipType: 'owned',
            additionalAssets: [],
          ),
          guarantor: models.Guarantor(
            id: '',
            name: '',
            nid: '',
            phoneNumber: '',
            address: '',
            relationship: '',
            occupation: '',
            monthlyIncome: 0,
            collateralPhotoUrl: '',
            collateralValue: 0,
          ),
          groupMembers: [],
          portfolioOfficer: models.PortfolioOfficer(
            id: '',
            name: '',
            email: '',
            phone: '',
          ),
        ),
      );

      return true;
    } catch (e) {
      state = state.copyWith(
        isSubmitting: false,
        error: e.toString(),
      );
      return false;
    }
  }

  /// Validate current step
  void _validateCurrentStep() {
    final errors = <String, String>{};
    bool isValid = true;
    final app = state.application;
    
    if (app == null) {
      isValid = false;
      return;
    }

    switch (state.currentStep) {
      case 0: // NID Scan & Info
        if (app.applicant.name.isEmpty) {
          errors['name'] = 'Name is required';
          isValid = false;
        }
        if (app.applicant.nationalId.isEmpty) {
          errors['nationalId'] = 'National ID is required';
          isValid = false;
        }
        if (app.applicant.phoneNumber.isEmpty) {
          errors['phoneNumber'] = 'Phone number is required';
          isValid = false;
        }
        break;
      case 1: // Group & Loan Details
        if (app.requestedAmount <= 0) {
          errors['requestedAmount'] = 'Requested amount must be greater than 0';
          isValid = false;
        }
        if (app.loanTerm <= 0) {
          errors['loanTerm'] = 'Loan term must be greater than 0';
          isValid = false;
        }
        break;
      case 2: // Business Details
        if (app.business.name.isEmpty) {
          errors['businessName'] = 'Business name is required';
          isValid = false;
        }
        if (app.business.monthlyIncome <= 0) {
          errors['monthlyIncome'] = 'Monthly income must be greater than 0';
          isValid = false;
        }
        break;
      case 3: // Family Details
        if (app.family.familyIncome < 0) {
          errors['expenses'] = 'Family income cannot be negative';
          isValid = false;
        }
        break;
      case 4: // Guarantor Details
        if (app.guarantor.name.isEmpty) {
          errors['guarantorName'] = 'Guarantor name is required';
          isValid = false;
        }
        if (app.guarantor.phoneNumber.isEmpty) {
          errors['guarantorPhone'] = 'Guarantor phone is required';
          isValid = false;
        }
        break;
    }

    state = state.copyWith(
      fieldErrors: errors,
      isValid: isValid,
    );
  }

  /// Clear error state
  void clearError() {
    state = state.copyWith(error: null);
  }
}

/// Provider for new application form state
final newApplicationProvider =
    StateNotifierProvider<NewApplicationNotifier, NewApplicationState>((ref) {
  final service = ref.watch(loanApplicationServiceProvider);
  return NewApplicationNotifier(service);
});

/// Provider for checking if current step is valid
final currentStepValidProvider = Provider<bool>((ref) {
  final newAppState = ref.watch(newApplicationProvider);
  return newAppState.isValid;
});
