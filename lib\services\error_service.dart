import 'dart:convert';
import 'package:flutter/foundation.dart';

/// Comprehensive error handling and logging service
class ErrorService {
  static final ErrorService _instance = ErrorService._internal();
  factory ErrorService() => _instance;
  ErrorService._internal();

  final List<AppError> _errorLog = [];
  final List<Function(AppError)> _errorListeners = [];
  bool _isInitialized = false;
  String? _userId;
  String? _sessionId;

  /// Initialize error service
  Future<void> initialize({
    String? userId,
    bool enableCrashReporting = true,
    bool enableAnalytics = true,
  }) async {
    if (_isInitialized) return;

    try {
      _userId = userId;
      _sessionId = _generateSessionId();

      // Setup Flutter error handling
      _setupFlutterErrorHandling();

      // Setup platform error handling
      _setupPlatformErrorHandling();

      // Initialize crash reporting (Firebase Crashlytics in production)
      if (enableCrashReporting) {
        await _initializeCrashReporting();
      }

      // Initialize analytics (Firebase Analytics in production)
      if (enableAnalytics) {
        await _initializeAnalytics();
      }

      _isInitialized = true;
      logInfo('ErrorService initialized successfully');
    } catch (e) {
      print('Failed to initialize ErrorService: $e');
    }
  }

  /// Setup Flutter error handling
  void _setupFlutterErrorHandling() {
    // Handle Flutter framework errors
    FlutterError.onError = (FlutterErrorDetails details) {
      final error = AppError(
        id: _generateErrorId(),
        type: ErrorType.flutter,
        severity: ErrorSeverity.high,
        message: details.exception.toString(),
        stackTrace: details.stack?.toString(),
        timestamp: DateTime.now(),
        context: {
          'library': details.library,
          'context': details.context?.toString(),
        },
        userId: _userId,
        sessionId: _sessionId,
      );

      _handleError(error);
    };

    // Handle platform dispatcher errors (for async errors)
    PlatformDispatcher.instance.onError = (error, stack) {
      final appError = AppError(
        id: _generateErrorId(),
        type: ErrorType.platform,
        severity: ErrorSeverity.high,
        message: error.toString(),
        stackTrace: stack.toString(),
        timestamp: DateTime.now(),
        userId: _userId,
        sessionId: _sessionId,
      );

      _handleError(appError);
      return true;
    };
  }

  /// Setup platform-specific error handling
  void _setupPlatformErrorHandling() {
    // Handle isolate errors
    if (!kIsWeb) {
      // Platform-specific error handling would go here
    }
  }

  /// Initialize crash reporting
  Future<void> _initializeCrashReporting() async {
    try {
      // In production, initialize Firebase Crashlytics:
      // await FirebaseCrashlytics.instance.setCrashlyticsCollectionEnabled(true);
      // if (_userId != null) {
      //   await FirebaseCrashlytics.instance.setUserIdentifier(_userId!);
      // }
      
      logInfo('Crash reporting initialized');
    } catch (e) {
      logError('Failed to initialize crash reporting: $e');
    }
  }

  /// Initialize analytics
  Future<void> _initializeAnalytics() async {
    try {
      // In production, initialize Firebase Analytics:
      // await FirebaseAnalytics.instance.setUserId(id: _userId);
      // await FirebaseAnalytics.instance.setSessionTimeoutDuration(Duration(minutes: 30));
      
      logInfo('Analytics initialized');
    } catch (e) {
      logError('Failed to initialize analytics: $e');
    }
  }

  /// Handle application errors
  void _handleError(AppError error) {
    // Add to local error log
    _errorLog.insert(0, error);
    
    // Limit error log size
    if (_errorLog.length > 1000) {
      _errorLog.removeRange(1000, _errorLog.length);
    }

    // Log to console in debug mode
    if (kDebugMode) {
      _logToConsole(error);
    }

    // Send to crash reporting service
    _sendToCrashReporting(error);

    // Send to analytics
    _sendToAnalytics(error);

    // Notify listeners
    for (final listener in _errorListeners) {
      try {
        listener(error);
      } catch (e) {
        print('Error in error listener: $e');
      }
    }

    // Handle critical errors
    if (error.severity == ErrorSeverity.critical) {
      _handleCriticalError(error);
    }
  }

  /// Log error to console
  void _logToConsole(AppError error) {
    final prefix = _getLogPrefix(error.severity);
    print('$prefix [${error.type.name.toUpperCase()}] ${error.message}');
    if (error.stackTrace != null) {
      print('Stack trace:\n${error.stackTrace}');
    }
    if (error.context.isNotEmpty) {
      print('Context: ${jsonEncode(error.context)}');
    }
  }

  /// Get log prefix based on severity
  String _getLogPrefix(ErrorSeverity severity) {
    switch (severity) {
      case ErrorSeverity.low:
        return '💡';
      case ErrorSeverity.medium:
        return '⚠️';
      case ErrorSeverity.high:
        return '🚨';
      case ErrorSeverity.critical:
        return '💥';
    }
  }

  /// Send error to crash reporting service
  void _sendToCrashReporting(AppError error) {
    try {
      // In production, send to Firebase Crashlytics:
      // FirebaseCrashlytics.instance.recordError(
      //   error.message,
      //   error.stackTrace != null ? StackTrace.fromString(error.stackTrace!) : null,
      //   fatal: error.severity == ErrorSeverity.critical,
      //   information: [
      //     DiagnosticsProperty('type', error.type.name),
      //     DiagnosticsProperty('severity', error.severity.name),
      //     DiagnosticsProperty('context', error.context),
      //   ],
      // );
    } catch (e) {
      print('Failed to send error to crash reporting: $e');
    }
  }

  /// Send error to analytics
  void _sendToAnalytics(AppError error) {
    try {
      // In production, send to Firebase Analytics:
      // FirebaseAnalytics.instance.logEvent(
      //   name: 'app_error',
      //   parameters: {
      //     'error_type': error.type.name,
      //     'error_severity': error.severity.name,
      //     'error_message': error.message.length > 100 
      //         ? error.message.substring(0, 100) 
      //         : error.message,
      //   },
      // );
    } catch (e) {
      print('Failed to send error to analytics: $e');
    }
  }

  /// Handle critical errors
  void _handleCriticalError(AppError error) {
    // In production, you might want to:
    // 1. Show a user-friendly error dialog
    // 2. Attempt to recover the app state
    // 3. Send immediate crash report
    // 4. Log user out if necessary
    
    logError('CRITICAL ERROR: ${error.message}');
  }

  /// Log different types of messages
  void logError(String message, {
    String? stackTrace,
    Map<String, dynamic>? context,
    ErrorType type = ErrorType.application,
  }) {
    final error = AppError(
      id: _generateErrorId(),
      type: type,
      severity: ErrorSeverity.high,
      message: message,
      stackTrace: stackTrace,
      timestamp: DateTime.now(),
      context: context ?? {},
      userId: _userId,
      sessionId: _sessionId,
    );

    _handleError(error);
  }

  void logWarning(String message, {
    Map<String, dynamic>? context,
  }) {
    final error = AppError(
      id: _generateErrorId(),
      type: ErrorType.application,
      severity: ErrorSeverity.medium,
      message: message,
      timestamp: DateTime.now(),
      context: context ?? {},
      userId: _userId,
      sessionId: _sessionId,
    );

    _handleError(error);
  }

  void logInfo(String message, {
    Map<String, dynamic>? context,
  }) {
    final error = AppError(
      id: _generateErrorId(),
      type: ErrorType.application,
      severity: ErrorSeverity.low,
      message: message,
      timestamp: DateTime.now(),
      context: context ?? {},
      userId: _userId,
      sessionId: _sessionId,
    );

    _handleError(error);
  }

  /// Log network errors
  void logNetworkError(
    String endpoint,
    int? statusCode,
    String message, {
    Map<String, dynamic>? requestData,
    Map<String, dynamic>? responseData,
  }) {
    final error = AppError(
      id: _generateErrorId(),
      type: ErrorType.network,
      severity: _getNetworkErrorSeverity(statusCode),
      message: 'Network Error: $message',
      timestamp: DateTime.now(),
      context: {
        'endpoint': endpoint,
        'status_code': statusCode,
        'request_data': requestData,
        'response_data': responseData,
      },
      userId: _userId,
      sessionId: _sessionId,
    );

    _handleError(error);
  }

  /// Get network error severity based on status code
  ErrorSeverity _getNetworkErrorSeverity(int? statusCode) {
    if (statusCode == null) return ErrorSeverity.high;
    
    if (statusCode >= 500) return ErrorSeverity.high;
    if (statusCode >= 400) return ErrorSeverity.medium;
    return ErrorSeverity.low;
  }

  /// Log user actions for debugging
  void logUserAction(
    String action,
    Map<String, dynamic> parameters,
  ) {
    final error = AppError(
      id: _generateErrorId(),
      type: ErrorType.userAction,
      severity: ErrorSeverity.low,
      message: 'User Action: $action',
      timestamp: DateTime.now(),
      context: parameters,
      userId: _userId,
      sessionId: _sessionId,
    );

    _handleError(error);
  }

  /// Log performance issues
  void logPerformanceIssue(
    String operation,
    Duration duration, {
    Map<String, dynamic>? context,
  }) {
    final error = AppError(
      id: _generateErrorId(),
      type: ErrorType.performance,
      severity: _getPerformanceSeverity(duration),
      message: 'Performance Issue: $operation took ${duration.inMilliseconds}ms',
      timestamp: DateTime.now(),
      context: {
        'operation': operation,
        'duration_ms': duration.inMilliseconds,
        ...?context,
      },
      userId: _userId,
      sessionId: _sessionId,
    );

    _handleError(error);
  }

  /// Get performance severity based on duration
  ErrorSeverity _getPerformanceSeverity(Duration duration) {
    if (duration.inMilliseconds > 5000) return ErrorSeverity.high;
    if (duration.inMilliseconds > 2000) return ErrorSeverity.medium;
    return ErrorSeverity.low;
  }

  /// Wrap async operations with error handling
  Future<T> wrapAsync<T>(
    Future<T> Function() operation, {
    String? operationName,
    Map<String, dynamic>? context,
  }) async {
    final stopwatch = Stopwatch()..start();
    
    try {
      final result = await operation();
      
      // Log performance if operation took too long
      stopwatch.stop();
      if (stopwatch.elapsed.inMilliseconds > 1000) {
        logPerformanceIssue(
          operationName ?? 'Async Operation',
          stopwatch.elapsed,
          context: context,
        );
      }
      
      return result;
    } catch (error, stackTrace) {
      stopwatch.stop();
      
      logError(
        'Error in ${operationName ?? "async operation"}: $error',
        stackTrace: stackTrace.toString(),
        context: context,
      );
      
      rethrow;
    }
  }

  /// Wrap synchronous operations with error handling
  T wrapSync<T>(
    T Function() operation, {
    String? operationName,
    Map<String, dynamic>? context,
  }) {
    final stopwatch = Stopwatch()..start();
    
    try {
      final result = operation();
      
      // Log performance if operation took too long
      stopwatch.stop();
      if (stopwatch.elapsed.inMilliseconds > 500) {
        logPerformanceIssue(
          operationName ?? 'Sync Operation',
          stopwatch.elapsed,
          context: context,
        );
      }
      
      return result;
    } catch (error, stackTrace) {
      stopwatch.stop();
      
      logError(
        'Error in ${operationName ?? "sync operation"}: $error',
        stackTrace: stackTrace.toString(),
        context: context,
      );
      
      rethrow;
    }
  }

  /// Get error statistics
  ErrorStatistics getErrorStatistics() {
    final now = DateTime.now();
    final last24Hours = now.subtract(const Duration(days: 1));
    final last7Days = now.subtract(const Duration(days: 7));

    final errors24h = _errorLog.where((e) => e.timestamp.isAfter(last24Hours)).toList();
    final errors7d = _errorLog.where((e) => e.timestamp.isAfter(last7Days)).toList();

    return ErrorStatistics(
      totalErrors: _errorLog.length,
      errorsLast24Hours: errors24h.length,
      errorsLast7Days: errors7d.length,
      criticalErrors: _errorLog.where((e) => e.severity == ErrorSeverity.critical).length,
      networkErrors: _errorLog.where((e) => e.type == ErrorType.network).length,
      performanceIssues: _errorLog.where((e) => e.type == ErrorType.performance).length,
      mostCommonErrors: _getMostCommonErrors(),
    );
  }

  /// Get most common errors
  List<String> _getMostCommonErrors() {
    final errorCounts = <String, int>{};
    
    for (final error in _errorLog.take(100)) {
      final key = error.message.length > 50 
          ? error.message.substring(0, 50)
          : error.message;
      errorCounts[key] = (errorCounts[key] ?? 0) + 1;
    }
    
    final sorted = errorCounts.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    return sorted.take(5).map((e) => e.key).toList();
  }

  /// Get errors by type
  List<AppError> getErrorsByType(ErrorType type) {
    return _errorLog.where((error) => error.type == type).toList();
  }

  /// Get errors by severity
  List<AppError> getErrorsBySeverity(ErrorSeverity severity) {
    return _errorLog.where((error) => error.severity == severity).toList();
  }

  /// Clear error log
  void clearErrorLog() {
    _errorLog.clear();
    logInfo('Error log cleared');
  }

  /// Export error log
  String exportErrorLog() {
    final export = {
      'session_id': _sessionId,
      'user_id': _userId,
      'exported_at': DateTime.now().toIso8601String(),
      'errors': _errorLog.map((e) => e.toJson()).toList(),
    };
    
    return jsonEncode(export);
  }

  /// Add error listener
  void addErrorListener(Function(AppError) listener) {
    _errorListeners.add(listener);
  }

  /// Remove error listener
  void removeErrorListener(Function(AppError) listener) {
    _errorListeners.remove(listener);
  }

  /// Generate unique error ID
  String _generateErrorId() {
    return 'err_${DateTime.now().millisecondsSinceEpoch}_${_errorLog.length}';
  }

  /// Generate session ID
  String _generateSessionId() {
    return 'session_${DateTime.now().millisecondsSinceEpoch}';
  }

  /// Update user ID
  void setUserId(String userId) {
    _userId = userId;
  }

  /// Get current session ID
  String? get sessionId => _sessionId;

  /// Get current user ID
  String? get userId => _userId;

  /// Dispose resources
  void dispose() {
    _errorListeners.clear();
    _errorLog.clear();
  }
}

/// Application error model
class AppError {
  final String id;
  final ErrorType type;
  final ErrorSeverity severity;
  final String message;
  final String? stackTrace;
  final DateTime timestamp;
  final Map<String, dynamic> context;
  final String? userId;
  final String? sessionId;

  const AppError({
    required this.id,
    required this.type,
    required this.severity,
    required this.message,
    this.stackTrace,
    required this.timestamp,
    this.context = const {},
    this.userId,
    this.sessionId,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.name,
      'severity': severity.name,
      'message': message,
      'stack_trace': stackTrace,
      'timestamp': timestamp.toIso8601String(),
      'context': context,
      'user_id': userId,
      'session_id': sessionId,
    };
  }

  factory AppError.fromJson(Map<String, dynamic> json) {
    return AppError(
      id: json['id'],
      type: ErrorType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => ErrorType.application,
      ),
      severity: ErrorSeverity.values.firstWhere(
        (e) => e.name == json['severity'],
        orElse: () => ErrorSeverity.medium,
      ),
      message: json['message'],
      stackTrace: json['stack_trace'],
      timestamp: DateTime.parse(json['timestamp']),
      context: Map<String, dynamic>.from(json['context'] ?? {}),
      userId: json['user_id'],
      sessionId: json['session_id'],
    );
  }
}

/// Error types
enum ErrorType {
  application,
  network,
  flutter,
  platform,
  userAction,
  performance,
  validation,
  authentication,
  authorization,
  database,
}

/// Error severity levels
enum ErrorSeverity {
  low,
  medium,
  high,
  critical,
}

/// Error statistics model
class ErrorStatistics {
  final int totalErrors;
  final int errorsLast24Hours;
  final int errorsLast7Days;
  final int criticalErrors;
  final int networkErrors;
  final int performanceIssues;
  final List<String> mostCommonErrors;

  const ErrorStatistics({
    required this.totalErrors,
    required this.errorsLast24Hours,
    required this.errorsLast7Days,
    required this.criticalErrors,
    required this.networkErrors,
    required this.performanceIssues,
    required this.mostCommonErrors,
  });

  Map<String, dynamic> toJson() {
    return {
      'total_errors': totalErrors,
      'errors_last_24_hours': errorsLast24Hours,
      'errors_last_7_days': errorsLast7Days,
      'critical_errors': criticalErrors,
      'network_errors': networkErrors,
      'performance_issues': performanceIssues,
      'most_common_errors': mostCommonErrors,
    };
  }
}

/// Custom exception for error service operations
class ErrorServiceException implements Exception {
  final String message;
  final String? code;

  const ErrorServiceException(this.message, [this.code]);

  @override
  String toString() => 'ErrorServiceException: $message';
}