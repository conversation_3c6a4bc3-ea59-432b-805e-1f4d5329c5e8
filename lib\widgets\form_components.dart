import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../services/theme_service.dart';
import '../utils/validation_utils.dart';

/// Import required for NumberFormat
import 'package:intl/intl.dart';

/// Comprehensive form components library for loan applications
class FormComponents {
  /// Enhanced text field with validation and formatting
  static Widget enhancedTextField({
    required String label,
    String? hint,
    String? initialValue,
    TextEditingController? controller,
    TextInputType? keyboardType,
    bool obscureText = false,
    bool enabled = true,
    bool readOnly = false,
    bool required = false,
    int? maxLines = 1,
    int? maxLength,
    String? Function(String?)? validator,
    void Function(String)? onChanged,
    void Function()? onTap,
    Widget? prefixIcon,
    Widget? suffixIcon,
    List<TextInputFormatter>? inputFormatters,
    FocusNode? focusNode,
    TextCapitalization textCapitalization = TextCapitalization.none,
    String? helperText,
    bool showCharacterCount = false,
    AutovalidateMode? autovalidateMode,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (required)
          RichText(
            text: TextSpan(
              text: label,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black87,
              ),
              children: const [
                TextSpan(
                  text: ' *',
                  style: TextStyle(color: Colors.red),
                ),
              ],
            ),
          )
        else
          Text(
            label,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
        const SizedBox(height: AppSpacing.extraSmall),
        TextFormField(
          controller: controller,
          initialValue: initialValue,
          keyboardType: keyboardType,
          obscureText: obscureText,
          enabled: enabled,
          readOnly: readOnly,
          maxLines: maxLines,
          maxLength: maxLength,
          validator: validator,
          onChanged: onChanged,
          onTap: onTap,
          inputFormatters: inputFormatters,
          focusNode: focusNode,
          textCapitalization: textCapitalization,
          autovalidateMode:
              autovalidateMode ?? AutovalidateMode.onUserInteraction,
          decoration: InputDecoration(
            hintText: hint,
            prefixIcon: prefixIcon,
            suffixIcon: suffixIcon,
            helperText: helperText,
            counterText: showCharacterCount ? null : '',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppSpacing.radiusMedium),
            ),
          ),
        ),
      ],
    );
  }

  /// Currency input field with formatting
  static Widget currencyField({
    required String label,
    String? hint,
    TextEditingController? controller,
    bool required = false,
    String? Function(String?)? validator,
    void Function(String)? onChanged,
    String currencySymbol = '৳',
    String? helperText,
    FocusNode? focusNode,
    BuildContext? context,
  }) {
    return enhancedTextField(
      label: label,
      hint: hint,
      controller: controller,
      keyboardType: const TextInputType.numberWithOptions(decimal: true),
      required: required,
      validator: validator ?? (value) {
        if (value == null || value.isEmpty) {
          return 'Loan amount is required';
        }
        try {
          final amount = double.parse(value.replaceAll(',', ''));
          final result = ValidationUtils.validateLoanAmount(amount);
          return result.isValid ? null : result.message;
        } catch (e) {
          return 'Please enter a valid amount';
        }
      },
      onChanged: onChanged,
      focusNode: focusNode,
      helperText: helperText,
      prefixIcon: Padding(
        padding: const EdgeInsets.all(AppSpacing.medium),
        child: Text(
          currencySymbol,
          style: context != null 
              ? Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  )
              : const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
        ),
      ),
      inputFormatters: [
        FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')),
        CurrencyInputFormatter(),
      ],
    );
  }

  /// Phone number input field with country code
  static Widget phoneField({
    required String label,
    String? hint,
    TextEditingController? controller,
    bool required = false,
    String? Function(String?)? validator,
    void Function(String)? onChanged,
    String countryCode = '+880',
    String? helperText,
    FocusNode? focusNode,
    BuildContext? context,
  }) {
    return enhancedTextField(
      label: label,
      hint: hint ?? '1XXXXXXXXX',
      controller: controller,
      keyboardType: TextInputType.phone,
      required: required,
      validator: validator ?? (value) {
        final result = ValidationUtils.validatePhoneNumber(value ?? '');
        return result.isValid ? null : result.message;
      },
      onChanged: onChanged,
      focusNode: focusNode,
      helperText: helperText,
      prefixIcon: Padding(
        padding: const EdgeInsets.all(AppSpacing.medium),
        child: Text(
          countryCode,
          style: context != null 
              ? Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  )
              : const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
        ),
      ),
      inputFormatters: [
        FilteringTextInputFormatter.digitsOnly,
        LengthLimitingTextInputFormatter(10),
        PhoneNumberFormatter(),
      ],
    );
  }

  /// NID input field with validation
  static Widget nidField({
    required String label,
    String? hint,
    TextEditingController? controller,
    bool required = false,
    String? Function(String?)? validator,
    void Function(String)? onChanged,
    String? helperText,
    FocusNode? focusNode,
    VoidCallback? onScanTap,
  }) {
    return enhancedTextField(
      label: label,
      hint: hint ?? 'Enter 10 or 13 digit NID',
      controller: controller,
      keyboardType: TextInputType.number,
      required: required,
      validator: validator ?? (value) => ValidationUtils.validateNidNumber(value ?? '').message,
      onChanged: onChanged,
      focusNode: focusNode,
      helperText: helperText,
      prefixIcon: const Icon(Icons.credit_card),
      suffixIcon: onScanTap != null
          ? IconButton(
              onPressed: onScanTap,
              icon: const Icon(Icons.camera_alt),
              tooltip: 'Scan NID',
            )
          : null,
      inputFormatters: [
        FilteringTextInputFormatter.digitsOnly,
        LengthLimitingTextInputFormatter(13),
      ],
    );
  }

  /// Date picker field
  static Widget dateField({
    required String label,
    String? hint,
    TextEditingController? controller,
    bool required = false,
    String? Function(String?)? validator,
    void Function(DateTime?)? onChanged,
    DateTime? initialDate,
    DateTime? firstDate,
    DateTime? lastDate,
    String? helperText,
    FocusNode? focusNode,
    BuildContext? context,
  }) {
    return enhancedTextField(
      label: label,
      hint: hint ?? 'DD/MM/YYYY',
      controller: controller,
      required: required,
      validator: validator ?? (value) {
        if (value == null || value.isEmpty) {
          return 'Date of birth is required';
        }
        // For string validation, we'll just check format
        try {
          final parts = value.split('/');
          if (parts.length != 3) return 'Invalid date format';
          final day = int.parse(parts[0]);
          final month = int.parse(parts[1]);
          final year = int.parse(parts[2]);
          final date = DateTime(year, month, day);
          final result = ValidationUtils.validateDateOfBirth(date);
          return result.isValid ? null : result.message;
        } catch (e) {
          return 'Invalid date format';
        }
      },
      focusNode: focusNode,
      helperText: helperText,
      readOnly: true,
      prefixIcon: const Icon(Icons.calendar_today),
      onTap: () async {
        if (context != null) {
          final date = await showDatePicker(
            context: context,
            initialDate: initialDate ?? DateTime.now(),
            firstDate: firstDate ?? DateTime(1900),
            lastDate: lastDate ?? DateTime.now(),
          );

          if (date != null) {
            controller?.text =
                '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
            onChanged?.call(date);
          }
        }
      },
    );
  }

  /// Dropdown field with search
  static Widget dropdownField<T>({
    required String label,
    String? hint,
    T? value,
    required List<DropdownMenuItem<T>> items,
    bool required = false,
    String? Function(T?)? validator,
    void Function(T?)? onChanged,
    String? helperText,
    Widget? prefixIcon,
    bool isSearchable = false,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (required)
          RichText(
            text: TextSpan(
              text: label,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black87,
              ),
              children: const [
                TextSpan(
                  text: ' *',
                  style: TextStyle(color: Colors.red),
                ),
              ],
            ),
          )
        else
          Text(
            label,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
        const SizedBox(height: AppSpacing.extraSmall),
        DropdownButtonFormField<T>(
          value: value,
          items: items,
          onChanged: onChanged,
          validator: validator,
          decoration: InputDecoration(
            hintText: hint,
            prefixIcon: prefixIcon,
            helperText: helperText,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppSpacing.radiusMedium),
            ),
          ),
          isExpanded: true,
        ),
      ],
    );
  }

  /// Multi-select chip field
  static Widget multiSelectChipField<T>({
    required String label,
    required List<T> options,
    required List<T> selectedValues,
    required String Function(T) getLabel,
    required void Function(List<T>) onChanged,
    bool required = false,
    String? helperText,
    int? maxSelections,
    bool allowSelectAll = false,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            if (required)
              RichText(
                text: TextSpan(
                  text: label,
                  style: const TextStyle(
                fontSize: 14,
                color: Colors.black87,
              ),
                  children: const [
                    TextSpan(
                      text: ' *',
                      style: TextStyle(color: Colors.red),
                    ),
                  ],
                ),
              )
            else
              Text(
                label,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Colors.black87,
                ),
              ),
            if (allowSelectAll)
              TextButton(
                onPressed: () {
                  if (selectedValues.length == options.length) {
                    onChanged([]);
                  } else {
                    onChanged(List.from(options));
                  }
                },
                child: Text(
                  selectedValues.length == options.length
                      ? 'Clear All'
                      : 'Select All',
                  style: const TextStyle(
                    fontSize: 11,
                    color: Colors.blue,
                  ),
                ),
              ),
          ],
        ),
        const SizedBox(height: AppSpacing.extraSmall),
        Wrap(
          spacing: AppSpacing.small,
          runSpacing: AppSpacing.extraSmall,
          children: options.map((option) {
            final isSelected = selectedValues.contains(option);
            final canSelect = maxSelections == null ||
                selectedValues.length < maxSelections ||
                isSelected;

            return FilterChip(
              label: Text(getLabel(option)),
              selected: isSelected,
              onSelected: canSelect
                  ? (selected) {
                      final newValues = List<T>.from(selectedValues);
                      if (selected) {
                        newValues.add(option);
                      } else {
                        newValues.remove(option);
                      }
                      onChanged(newValues);
                    }
                  : null,
            );
          }).toList(),
        ),
        if (helperText != null) ...[
          const SizedBox(height: AppSpacing.extraSmall),
          Text(
            helperText,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
        ],
        if (maxSelections != null) ...[
          const SizedBox(height: AppSpacing.extraSmall),
          Text(
            'Selected: ${selectedValues.length}/$maxSelections',
            style: TextStyle(
              fontSize: 12,
              color: selectedValues.length >= maxSelections
                  ? Colors.orange
                  : Colors.grey[600],
            ),
          ),
        ],
      ],
    );
  }

  /// File upload field
  static Widget fileUploadField({
    required String label,
    required List<String> allowedExtensions,
    List<String> uploadedFiles = const [],
    required void Function(List<String>) onFilesChanged,
    bool required = false,
    bool multiple = false,
    int? maxFiles,
    String? helperText,
    double maxSizeInMB = 5.0,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (required)
          RichText(
            text: TextSpan(
              text: label,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black87,
              ),
              children: const [
                TextSpan(
                  text: ' *',
                  style: TextStyle(color: Colors.red),
                ),
              ],
            ),
          )
        else
          Text(
            label,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
        const SizedBox(height: AppSpacing.extraSmall),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(AppSpacing.medium),
          decoration: BoxDecoration(
            border: Border.all(
              color: Colors.grey[300]!,
              style: BorderStyle.solid,
            ),
            borderRadius: BorderRadius.circular(AppSpacing.radiusMedium),
          ),
          child: Column(
            children: [
              Icon(
                Icons.cloud_upload_outlined,
                size: 48,
                color: Colors.grey[400],
              ),
              const SizedBox(height: AppSpacing.small),
              Text(
                'Tap to upload ${multiple ? 'files' : 'file'}',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              ),
              const SizedBox(height: AppSpacing.extraSmall),
              Text(
                'Allowed: ${allowedExtensions.join(', ')}',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[500],
                ),
              ),
              Text(
                'Max size: ${maxSizeInMB}MB',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[500],
                ),
              ),
            ],
          ),
        ),
        if (uploadedFiles.isNotEmpty) ...[
          const SizedBox(height: AppSpacing.small),
          ...uploadedFiles.map((file) => Container(
                margin: const EdgeInsets.only(bottom: AppSpacing.extraSmall),
                padding: const EdgeInsets.all(AppSpacing.small),
                decoration: BoxDecoration(
                  color: Colors.green[50],
                  borderRadius: BorderRadius.circular(AppSpacing.radiusSmall),
                  border: Border.all(color: Colors.green[200]!),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.insert_drive_file,
                      color: Colors.green[600],
                      size: 16,
                    ),
                    const SizedBox(width: AppSpacing.small),
                    Expanded(
                      child: Text(
                        file,
                        style: const TextStyle(
                          fontSize: 12,
                          color: Colors.black87,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    IconButton(
                      onPressed: () {
                        final newFiles = List<String>.from(uploadedFiles);
                        newFiles.remove(file);
                        onFilesChanged(newFiles);
                      },
                      icon: Icon(
                        Icons.close,
                        color: Colors.red[600],
                        size: 16,
                      ),
                      constraints: const BoxConstraints(
                        minWidth: 24,
                        minHeight: 24,
                      ),
                      padding: EdgeInsets.zero,
                    ),
                  ],
                ),
              )),
        ],
        if (helperText != null) ...[
          const SizedBox(height: AppSpacing.extraSmall),
          Text(
            helperText,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
        ],
        if (maxFiles != null && uploadedFiles.isNotEmpty) ...[
          const SizedBox(height: AppSpacing.extraSmall),
          Text(
            'Files: ${uploadedFiles.length}/$maxFiles',
            style: TextStyle(
              fontSize: 12,
              color: uploadedFiles.length >= maxFiles
                  ? Colors.orange
                  : Colors.grey[600],
            ),
          ),
        ],
      ],
    );
  }

  /// Form section with collapsible header
  static Widget formSection({
    required String title,
    required List<Widget> children,
    IconData? icon,
    bool initiallyExpanded = true,
    bool isRequired = false,
    String? subtitle,
    EdgeInsetsGeometry? padding,
  }) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppSpacing.medium),
      child: ExpansionTile(
        title: Row(
          children: [
            if (icon != null) ...[
              Icon(icon, size: 20),
              const SizedBox(width: AppSpacing.small),
            ],
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (isRequired)
                    RichText(
                      text: TextSpan(
                        text: title,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.black87,
                        ),
                        children: const [
                          TextSpan(
                            text: ' *',
                            style: TextStyle(color: Colors.red),
                          ),
                        ],
                      ),
                    )
                  else
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.black87,
                      ),
                    ),
                  if (subtitle != null)
                    Text(
                      subtitle,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
        initiallyExpanded: initiallyExpanded,
        childrenPadding: padding ?? const EdgeInsets.all(AppSpacing.medium),
        children: [
          Column(
            children: children
                .map((child) => Padding(
                      padding: const EdgeInsets.only(bottom: AppSpacing.medium),
                      child: child,
                    ))
                .toList(),
          ),
        ],
      ),
    );
  }

  /// Form progress indicator
  static Widget formProgress({
    required int currentStep,
    required int totalSteps,
    List<String>? stepLabels,
    Color? activeColor,
    Color? inactiveColor,
  }) {
    return Column(
      children: [
        Row(
          children: List.generate(totalSteps, (index) {
            final isActive = index <= currentStep;
            final isCompleted = index < currentStep;

            return Expanded(
              child: Row(
                children: [
                  Expanded(
                    child: Container(
                      height: 4,
                      decoration: BoxDecoration(
                        color: isActive
                            ? (activeColor ?? Colors.blue)
                            : (inactiveColor ?? Colors.grey[300]),
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                  ),
                  if (index < totalSteps - 1)
                    Container(
                      width: 20,
                      height: 20,
                      margin: const EdgeInsets.symmetric(horizontal: 4),
                      decoration: BoxDecoration(
                        color: isCompleted
                            ? (activeColor ?? Colors.blue)
                            : (inactiveColor ?? Colors.grey[300]),
                        shape: BoxShape.circle,
                      ),
                      child: isCompleted
                          ? const Icon(
                              Icons.check,
                              color: Colors.white,
                              size: 12,
                            )
                          : Text(
                              '${index + 1}',
                              style: TextStyle(
                                color:
                                    isActive ? Colors.white : Colors.grey[600],
                                fontSize: 10,
                                fontWeight: FontWeight.w600,
                              ),
                              textAlign: TextAlign.center,
                            ),
                    ),
                ],
              ),
            );
          }),
        ),
        if (stepLabels != null) ...[
          const SizedBox(height: AppSpacing.small),
          Row(
            children: stepLabels.asMap().entries.map((entry) {
              final index = entry.key;
              final label = entry.value;
              final isActive = index <= currentStep;

              return Expanded(
                child: Text(
                  label,
                  style: TextStyle(
                    fontSize: 12,
                    color: isActive
                        ? (activeColor ?? Colors.blue)
                        : (inactiveColor ?? Colors.grey[500]),
                    fontWeight:
                        isActive ? FontWeight.w600 : FontWeight.normal,
                  ),
                  textAlign: TextAlign.center,
                ),
              );
            }).toList(),
          ),
        ],
      ],
    );
  }
}

/// Custom input formatters
class CurrencyInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    if (newValue.text.isEmpty) {
      return newValue;
    }

    final value = double.tryParse(newValue.text.replaceAll(',', ''));
    if (value == null) {
      return oldValue;
    }

    final formatter = NumberFormat('#,##0.00', 'en_US');
    final newText = formatter.format(value);

    return newValue.copyWith(
      text: newText,
      selection: TextSelection.collapsed(offset: newText.length),
    );
  }
}

class PhoneNumberFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    final text = newValue.text;

    if (text.length <= 3) {
      return newValue;
    }

    String formatted = '';
    for (int i = 0; i < text.length; i++) {
      if (i == 3 || i == 6) {
        formatted += '-';
      }
      formatted += text[i];
    }

    return newValue.copyWith(
      text: formatted,
      selection: TextSelection.collapsed(offset: formatted.length),
    );
  }
}
