import 'dart:convert';
import 'package:flutter/foundation.dart';
import '../models/loan_application.dart';

/// Comprehensive analytics service for tracking user behavior and app performance
class AnalyticsService {
  static final AnalyticsService _instance = AnalyticsService._internal();
  factory AnalyticsService() => _instance;
  AnalyticsService._internal();

  bool _isInitialized = false;
  String? _userId;
  String? _sessionId;
  DateTime? _sessionStartTime;
  final List<AnalyticsEvent> _eventQueue = [];
  final Map<String, dynamic> _userProperties = {};
  final Map<String, int> _screenViews = {};
  final Map<String, Duration> _screenTimes = {};
  String? _currentScreen;
  DateTime? _currentScreenStartTime;

  /// Initialize analytics service
  Future<void> initialize({
    String? userId,
    Map<String, dynamic>? userProperties,
    bool enableDebugLogging = false,
  }) async {
    if (_isInitialized) return;

    try {
      _userId = userId;
      _sessionId = _generateSessionId();
      _sessionStartTime = DateTime.now();
      
      if (userProperties != null) {
        _userProperties.addAll(userProperties);
      }

      // Initialize Firebase Analytics in production
      await _initializeFirebaseAnalytics();

      // Initialize custom analytics
      await _initializeCustomAnalytics();

      // Start session tracking
      await _startSession();

      _isInitialized = true;
      
      if (enableDebugLogging) {
        print('AnalyticsService initialized successfully');
      }
    } catch (e) {
      print('Failed to initialize AnalyticsService: $e');
    }
  }

  /// Initialize Firebase Analytics
  Future<void> _initializeFirebaseAnalytics() async {
    try {
      // In production, initialize Firebase Analytics:
      // await FirebaseAnalytics.instance.setUserId(id: _userId);
      // await FirebaseAnalytics.instance.setSessionTimeoutDuration(Duration(minutes: 30));
      // 
      // // Set user properties
      // for (final entry in _userProperties.entries) {
      //   await FirebaseAnalytics.instance.setUserProperty(
      //     name: entry.key,
      //     value: entry.value?.toString(),
      //   );
      // }
      
      print('Firebase Analytics initialized');
    } catch (e) {
      print('Failed to initialize Firebase Analytics: $e');
    }
  }

  /// Initialize custom analytics
  Future<void> _initializeCustomAnalytics() async {
    try {
      // Initialize custom analytics backend
      // This could be your own analytics service or third-party like Mixpanel
      
      print('Custom analytics initialized');
    } catch (e) {
      print('Failed to initialize custom analytics: $e');
    }
  }

  /// Start analytics session
  Future<void> _startSession() async {
    await trackEvent(
        'session_start',
        parameters: {
          'session_id': _sessionId,
          'user_id': _userId,
          'platform': kIsWeb ? 'web' : 'mobile',
          'app_version': '1.0.0', // Get from package_info in production
        },
      );
  }

  /// Track custom event
  Future<void> trackEvent(
    String eventName, {
    Map<String, dynamic>? parameters,
    bool immediate = false,
  }) async {
    if (!_isInitialized) {
      print('AnalyticsService not initialized');
      return;
    }

    final event = AnalyticsEvent(
      name: eventName,
      parameters: {
        'timestamp': DateTime.now().toIso8601String(),
        'session_id': _sessionId,
        'user_id': _userId,
        ...?parameters,
      },
      timestamp: DateTime.now(),
    );

    // Add to queue
    _eventQueue.add(event);

    // Send immediately if requested or queue is full
    if (immediate || _eventQueue.length >= 10) {
      await _flushEvents();
    }

    // Log in debug mode
    if (kDebugMode) {
      print('📊 Analytics Event: $eventName');
      if (parameters != null && parameters.isNotEmpty) {
        print('   Parameters: ${jsonEncode(parameters)}');
      }
    }
  }

  /// Track screen view
  Future<void> trackScreenView(
    String screenName, {
    Map<String, dynamic>? parameters,
  }) async {
    // End previous screen tracking
    if (_currentScreen != null && _currentScreenStartTime != null) {
      final timeSpent = DateTime.now().difference(_currentScreenStartTime!);
      _screenTimes[_currentScreen!] = 
          (_screenTimes[_currentScreen!] ?? Duration.zero) + timeSpent;
      
      await trackEvent(
        'screen_view_end',
        parameters: {
          'screen_name': _currentScreen,
          'time_spent_seconds': timeSpent.inSeconds,
        },
      );
    }

    // Start new screen tracking
    _currentScreen = screenName;
    _currentScreenStartTime = DateTime.now();
    _screenViews[screenName] = (_screenViews[screenName] ?? 0) + 1;

    await trackEvent(
      'screen_view',
      parameters: {
        'screen_name': screenName,
        'screen_class': screenName,
        'view_count': _screenViews[screenName],
        ...?parameters,
      },
    );
  }

  /// Track user action
  Future<void> trackUserAction(
    String action, {
    String? category,
    String? label,
    int? value,
    Map<String, dynamic>? parameters,
  }) async {
    await trackEvent(
      'user_action',
      parameters: {
        'action': action,
        'category': category,
        'label': label,
        'value': value,
        'screen_name': _currentScreen,
        ...?parameters,
      },
    );
  }

  /// Track button click
  Future<void> trackButtonClick(
    String buttonName, {
    String? screenName,
    Map<String, dynamic>? parameters,
  }) async {
    await trackUserAction(
      'button_click',
      category: 'ui_interaction',
      label: buttonName,
      parameters: {
        'button_name': buttonName,
        'screen_name': screenName ?? _currentScreen,
        ...?parameters,
      },
    );
  }

  /// Track form submission
  Future<void> trackFormSubmission(
    String formName, {
    bool success = true,
    String? errorMessage,
    Map<String, dynamic>? formData,
  }) async {
    await trackEvent(
      'form_submission',
      parameters: {
        'form_name': formName,
        'success': success,
        'error_message': errorMessage,
        'screen_name': _currentScreen,
        'form_fields_count': formData?.length,
        // Don't include actual form data for privacy
      },
    );
  }

  /// Track search
  Future<void> trackSearch(
    String query, {
    String? category,
    int? resultsCount,
  }) async {
    await trackEvent(
      'search',
      parameters: {
        'search_term': query.length > 50 ? query.substring(0, 50) : query,
        'search_category': category,
        'results_count': resultsCount,
        'screen_name': _currentScreen,
      },
    );
  }

  /// Track loan application events
  Future<void> trackLoanApplicationCreated(
    String applicationId,
    double amount,
    int termMonths,
  ) async {
    await trackEvent(
      'loan_application_created',
      parameters: {
        'application_id': applicationId,
        'loan_amount': amount,
        'loan_term_months': termMonths,
        'application_method': 'mobile_app',
      },
    );
  }

  Future<void> trackLoanApplicationStatusChange(
    String applicationId,
    LoanStatus oldStatus,
    LoanStatus newStatus,
  ) async {
    await trackEvent(
      'loan_application_status_change',
      parameters: {
        'application_id': applicationId,
        'old_status': oldStatus.name,
        'new_status': newStatus.name,
        'status_change_direction': _getStatusChangeDirection(oldStatus, newStatus),
      },
    );
  }

  Future<void> trackLoanApplicationStep(
    String applicationId,
    int stepNumber,
    String stepName,
    bool completed,
  ) async {
    await trackEvent(
      'loan_application_step',
      parameters: {
        'application_id': applicationId,
        'step_number': stepNumber,
        'step_name': stepName,
        'completed': completed,
        'total_steps': 7, // Based on your application flow
      },
    );
  }

  /// Track NID scanning
  Future<void> trackNIDScan(
    bool success, {
    String? errorMessage,
    Duration? scanDuration,
  }) async {
    await trackEvent(
      'nid_scan',
      parameters: {
        'success': success,
        'error_message': errorMessage,
        'scan_duration_ms': scanDuration?.inMilliseconds,
        'scan_method': 'ocr',
      },
    );
  }

  /// Track photo capture
  Future<void> trackPhotoCapture(
    String photoType, {
    bool success = true,
    String? source, // 'camera' or 'gallery'
  }) async {
    await trackEvent(
      'photo_capture',
      parameters: {
        'photo_type': photoType,
        'success': success,
        'source': source,
        'screen_name': _currentScreen,
      },
    );
  }

  /// Track performance metrics
  Future<void> trackPerformance(
    String operation,
    Duration duration, {
    bool success = true,
    Map<String, dynamic>? metadata,
  }) async {
    await trackEvent(
      'performance_metric',
      parameters: {
        'operation': operation,
        'duration_ms': duration.inMilliseconds,
        'success': success,
        'screen_name': _currentScreen,
        ...?metadata,
      },
    );
  }

  /// Track errors
  Future<void> trackError(
    String errorType,
    String errorMessage, {
    String? stackTrace,
    Map<String, dynamic>? context,
  }) async {
    await trackEvent(
      'app_error',
      parameters: {
        'error_type': errorType,
        'error_message': errorMessage.length > 100 
            ? errorMessage.substring(0, 100) 
            : errorMessage,
        'has_stack_trace': stackTrace != null,
        'screen_name': _currentScreen,
        'context_keys': context?.keys.toList(),
      },
    );
  }

  /// Track user engagement
  Future<void> trackEngagement(
    String engagementType, {
    Duration? duration,
    Map<String, dynamic>? parameters,
  }) async {
    await trackEvent(
      'user_engagement',
      parameters: {
        'engagement_type': engagementType,
        'duration_seconds': duration?.inSeconds,
        'screen_name': _currentScreen,
        ...?parameters,
      },
    );
  }

  /// Set user properties
  Future<void> setUserProperty(String name, dynamic value) async {
    _userProperties[name] = value;

    // Update in Firebase Analytics
    try {
      // await FirebaseAnalytics.instance.setUserProperty(
      //   name: name,
      //   value: value?.toString(),
      // );
    } catch (e) {
      print('Failed to set user property: $e');
    }
  }

  /// Set multiple user properties
  Future<void> setUserProperties(Map<String, dynamic> properties) async {
    for (final entry in properties.entries) {
      await setUserProperty(entry.key, entry.value);
    }
  }

  /// Track conversion events
  Future<void> trackConversion(
    String conversionType,
    double value, {
    String? currency = 'BDT',
    Map<String, dynamic>? parameters,
  }) async {
    await trackEvent(
      'conversion',
      parameters: {
        'conversion_type': conversionType,
        'value': value,
        'currency': currency,
        'screen_name': _currentScreen,
        ...?parameters,
      },
    );
  }

  /// Track revenue events
  Future<void> trackRevenue(
    double amount,
    String source, {
    String? currency = 'BDT',
    String? transactionId,
  }) async {
    await trackEvent(
      'revenue',
      parameters: {
        'amount': amount,
        'currency': currency,
        'source': source,
        'transaction_id': transactionId,
      },
    );
  }

  /// Get session analytics
  SessionAnalytics getSessionAnalytics() {
    final now = DateTime.now();
    final sessionDuration = _sessionStartTime != null 
        ? now.difference(_sessionStartTime!) 
        : Duration.zero;

    return SessionAnalytics(
      sessionId: _sessionId ?? '',
      userId: _userId,
      sessionDuration: sessionDuration,
      screenViews: Map.from(_screenViews),
      screenTimes: Map.from(_screenTimes),
      eventsCount: _eventQueue.length,
      currentScreen: _currentScreen,
    );
  }

  /// Get user analytics summary
  UserAnalyticsSummary getUserAnalyticsSummary() {
    final totalScreenTime = _screenTimes.values
        .fold(Duration.zero, (sum, duration) => sum + duration);
    
    final mostViewedScreen = _screenViews.entries
        .fold<MapEntry<String, int>?>(null, (prev, current) {
      if (prev == null || current.value > prev.value) {
        return current;
      }
      return prev;
    });

    return UserAnalyticsSummary(
      userId: _userId,
      totalScreenViews: _screenViews.values.fold(0, (sum, count) => sum + count),
      totalScreenTime: totalScreenTime,
      uniqueScreensVisited: _screenViews.length,
      mostViewedScreen: mostViewedScreen?.key,
      averageSessionDuration: _sessionStartTime != null 
          ? DateTime.now().difference(_sessionStartTime!) 
          : Duration.zero,
      userProperties: Map.from(_userProperties),
    );
  }

  /// Flush events to analytics services
  Future<void> _flushEvents() async {
    if (_eventQueue.isEmpty) return;

    try {
      final eventsToSend = List<AnalyticsEvent>.from(_eventQueue);
      _eventQueue.clear();

      // Send to Firebase Analytics
      for (final event in eventsToSend) {
        await _sendToFirebaseAnalytics(event);
      }

      // Send to custom analytics
      await _sendToCustomAnalytics(eventsToSend);

      if (kDebugMode) {
        print('📊 Flushed ${eventsToSend.length} analytics events');
      }
    } catch (e) {
      print('Failed to flush analytics events: $e');
      // Re-add events to queue for retry
      // _eventQueue.addAll(eventsToSend);
    }
  }

  /// Send event to Firebase Analytics
  Future<void> _sendToFirebaseAnalytics(AnalyticsEvent event) async {
    try {
      // await FirebaseAnalytics.instance.logEvent(
      //   name: event.name,
      //   parameters: event.parameters,
      // );
    } catch (e) {
      print('Failed to send event to Firebase Analytics: $e');
    }
  }

  /// Send events to custom analytics
  Future<void> _sendToCustomAnalytics(List<AnalyticsEvent> events) async {
    try {
      // Send to your custom analytics backend
      // final payload = {
      //   'events': events.map((e) => e.toJson()).toList(),
      //   'session_id': _sessionId,
      //   'user_id': _userId,
      //   'timestamp': DateTime.now().toIso8601String(),
      // };
      // 
      // await http.post(
      //   Uri.parse('$analyticsEndpoint/events'),
      //   headers: {'Content-Type': 'application/json'},
      //   body: jsonEncode(payload),
      // );
    } catch (e) {
      print('Failed to send events to custom analytics: $e');
    }
  }

  /// Get status change direction
  String _getStatusChangeDirection(LoanStatus oldStatus, LoanStatus newStatus) {
    final statusOrder = {
      LoanStatus.pending: 0,
      LoanStatus.approved: 1,
      LoanStatus.disbursed: 2,
      LoanStatus.rejected: -1,
    };

    final oldOrder = statusOrder[oldStatus] ?? 0;
    final newOrder = statusOrder[newStatus] ?? 0;

    if (newOrder > oldOrder) return 'forward';
    if (newOrder < oldOrder) return 'backward';
    return 'same';
  }

  /// Generate session ID
  String _generateSessionId() {
    return 'session_${DateTime.now().millisecondsSinceEpoch}';
  }

  /// End current session
  Future<void> endSession() async {
    if (_sessionStartTime != null) {
      final sessionDuration = DateTime.now().difference(_sessionStartTime!);
      
      await trackEvent(
        'session_end',
        parameters: {
          'session_duration_seconds': sessionDuration.inSeconds,
          'screen_views_count': _screenViews.values.fold(0, (sum, count) => sum + count),
          'unique_screens_visited': _screenViews.length,
        },
        immediate: true,
      );
    }

    // Flush remaining events
    await _flushEvents();
  }

  /// Reset analytics data
  void reset() {
    _eventQueue.clear();
    _screenViews.clear();
    _screenTimes.clear();
    _userProperties.clear();
    _currentScreen = null;
    _currentScreenStartTime = null;
    _sessionStartTime = null;
    _userId = null;
    _sessionId = _generateSessionId();
  }

  /// Dispose resources
  void dispose() {
    _flushEvents();
    reset();
  }
}

/// Analytics event model
class AnalyticsEvent {
  final String name;
  final Map<String, dynamic> parameters;
  final DateTime timestamp;

  const AnalyticsEvent({
    required this.name,
    required this.parameters,
    required this.timestamp,
  });

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'parameters': parameters,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  factory AnalyticsEvent.fromJson(Map<String, dynamic> json) {
    return AnalyticsEvent(
      name: json['name'],
      parameters: Map<String, dynamic>.from(json['parameters']),
      timestamp: DateTime.parse(json['timestamp']),
    );
  }
}

/// Session analytics model
class SessionAnalytics {
  final String sessionId;
  final String? userId;
  final Duration sessionDuration;
  final Map<String, int> screenViews;
  final Map<String, Duration> screenTimes;
  final int eventsCount;
  final String? currentScreen;

  const SessionAnalytics({
    required this.sessionId,
    this.userId,
    required this.sessionDuration,
    required this.screenViews,
    required this.screenTimes,
    required this.eventsCount,
    this.currentScreen,
  });

  Map<String, dynamic> toJson() {
    return {
      'session_id': sessionId,
      'user_id': userId,
      'session_duration_seconds': sessionDuration.inSeconds,
      'screen_views': screenViews,
      'screen_times': screenTimes.map((k, v) => MapEntry(k, v.inSeconds)),
      'events_count': eventsCount,
      'current_screen': currentScreen,
    };
  }
}

/// User analytics summary model
class UserAnalyticsSummary {
  final String? userId;
  final int totalScreenViews;
  final Duration totalScreenTime;
  final int uniqueScreensVisited;
  final String? mostViewedScreen;
  final Duration averageSessionDuration;
  final Map<String, dynamic> userProperties;

  const UserAnalyticsSummary({
    this.userId,
    required this.totalScreenViews,
    required this.totalScreenTime,
    required this.uniqueScreensVisited,
    this.mostViewedScreen,
    required this.averageSessionDuration,
    required this.userProperties,
  });

  Map<String, dynamic> toJson() {
    return {
      'user_id': userId,
      'total_screen_views': totalScreenViews,
      'total_screen_time_seconds': totalScreenTime.inSeconds,
      'unique_screens_visited': uniqueScreensVisited,
      'most_viewed_screen': mostViewedScreen,
      'average_session_duration_seconds': averageSessionDuration.inSeconds,
      'user_properties': userProperties,
    };
  }
}

/// Custom exception for analytics operations
class AnalyticsException implements Exception {
  final String message;
  final String? code;

  const AnalyticsException(this.message, [this.code]);

  @override
  String toString() => 'AnalyticsException: $message';
}