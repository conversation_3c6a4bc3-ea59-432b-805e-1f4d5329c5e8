import '../models/loan_application.dart';

/// Comprehensive notification service for the loan application system
class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  // Notification channels
  static const String _applicationUpdatesChannel = 'application_updates';
  static const String _remindersChannel = 'reminders';
  static const String _systemChannel = 'system';
  static const String _marketingChannel = 'marketing';

  bool _isInitialized = false;
  String? _fcmToken;
  final List<AppNotification> _notifications = [];
  final List<Function(AppNotification)> _listeners = [];

  /// Initialize notification service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Initialize Firebase Cloud Messaging (FCM)
      await _initializeFCM();

      // Initialize local notifications
      await _initializeLocalNotifications();

      // Setup notification channels
      await _setupNotificationChannels();

      // Request permissions
      await _requestPermissions();

      _isInitialized = true;
      print('NotificationService initialized successfully');
    } catch (e) {
      print('Failed to initialize NotificationService: $e');
      throw NotificationException('Initialization failed: $e');
    }
  }

  /// Initialize Firebase Cloud Messaging
  Future<void> _initializeFCM() async {
    try {
      // In production, use firebase_messaging package:
      // final messaging = FirebaseMessaging.instance;
      // _fcmToken = await messaging.getToken();

      // For demo purposes, simulate FCM token
      _fcmToken = 'demo_fcm_token_${DateTime.now().millisecondsSinceEpoch}';

      // Setup message handlers
      _setupMessageHandlers();
    } catch (e) {
      throw NotificationException('FCM initialization failed: $e');
    }
  }

  /// Initialize local notifications
  Future<void> _initializeLocalNotifications() async {
    try {
      // In production, use flutter_local_notifications package:
      // const androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
      // const iosSettings = DarwinInitializationSettings();
      // const initSettings = InitializationSettings(
      //   android: androidSettings,
      //   iOS: iosSettings,
      // );
      // await flutterLocalNotificationsPlugin.initialize(initSettings);

      print('Local notifications initialized');
    } catch (e) {
      throw NotificationException(
          'Local notifications initialization failed: $e');
    }
  }

  /// Setup notification channels
  Future<void> _setupNotificationChannels() async {
    final channels = [
      const NotificationChannel(
        id: _applicationUpdatesChannel,
        name: 'Application Updates',
        description: 'Notifications about loan application status changes',
        importance: NotificationImportance.high,
      ),
      const NotificationChannel(
        id: _remindersChannel,
        name: 'Reminders',
        description: 'Reminders for pending actions',
        importance: NotificationImportance.medium,
      ),
      const NotificationChannel(
        id: _systemChannel,
        name: 'System',
        description: 'System notifications and updates',
        importance: NotificationImportance.low,
      ),
      const NotificationChannel(
        id: _marketingChannel,
        name: 'Promotions',
        description: 'Marketing and promotional notifications',
        importance: NotificationImportance.low,
      ),
    ];

    for (final channel in channels) {
      await _createNotificationChannel(channel);
    }
  }

  /// Create notification channel
  Future<void> _createNotificationChannel(NotificationChannel channel) async {
    // In production, create actual notification channels
    print('Created notification channel: ${channel.name}');
  }

  /// Request notification permissions
  Future<bool> _requestPermissions() async {
    try {
      // In production, request actual permissions:
      // final messaging = FirebaseMessaging.instance;
      // final settings = await messaging.requestPermission(
      //   alert: true,
      //   badge: true,
      //   sound: true,
      // );
      // return settings.authorizationStatus == AuthorizationStatus.authorized;

      // For demo, always return true
      return true;
    } catch (e) {
      print('Failed to request permissions: $e');
      return false;
    }
  }

  /// Setup message handlers for FCM
  void _setupMessageHandlers() {
    // In production, setup actual FCM handlers:
    // FirebaseMessaging.onMessage.listen(_handleForegroundMessage);
    // FirebaseMessaging.onMessageOpenedApp.listen(_handleMessageOpenedApp);
    // FirebaseMessaging.onBackgroundMessage(_handleBackgroundMessage);
  }

  /// Send application status notification
  Future<void> sendApplicationStatusNotification(
    String applicationId,
    LoanStatus oldStatus,
    LoanStatus newStatus,
    String applicantName,
  ) async {
    final notification = AppNotification(
      id: _generateNotificationId(),
      title: _getStatusChangeTitle(newStatus),
      body: _getStatusChangeBody(applicantName, oldStatus, newStatus),
      type: NotificationType.applicationUpdate,
      data: {
        'application_id': applicationId,
        'old_status': oldStatus.name,
        'new_status': newStatus.name,
      },
      timestamp: DateTime.now(),
      channelId: _applicationUpdatesChannel,
    );

    await _sendNotification(notification);
  }

  /// Send reminder notification
  Future<void> sendReminderNotification(
    String title,
    String body, {
    Map<String, dynamic>? data,
    DateTime? scheduledTime,
  }) async {
    final notification = AppNotification(
      id: _generateNotificationId(),
      title: title,
      body: body,
      type: NotificationType.reminder,
      data: data ?? {},
      timestamp: scheduledTime ?? DateTime.now(),
      channelId: _remindersChannel,
    );

    if (scheduledTime != null && scheduledTime.isAfter(DateTime.now())) {
      await _scheduleNotification(notification, scheduledTime);
    } else {
      await _sendNotification(notification);
    }
  }

  /// Send system notification
  Future<void> sendSystemNotification(
    String title,
    String body, {
    Map<String, dynamic>? data,
  }) async {
    final notification = AppNotification(
      id: _generateNotificationId(),
      title: title,
      body: body,
      type: NotificationType.system,
      data: data ?? {},
      timestamp: DateTime.now(),
      channelId: _systemChannel,
    );

    await _sendNotification(notification);
  }

  /// Send marketing notification
  Future<void> sendMarketingNotification(
    String title,
    String body, {
    Map<String, dynamic>? data,
    String? imageUrl,
  }) async {
    final notification = AppNotification(
      id: _generateNotificationId(),
      title: title,
      body: body,
      type: NotificationType.marketing,
      data: data ?? {},
      timestamp: DateTime.now(),
      channelId: _marketingChannel,
      imageUrl: imageUrl,
    );

    await _sendNotification(notification);
  }

  /// Send notification
  Future<void> _sendNotification(AppNotification notification) async {
    try {
      // Add to local storage
      _notifications.insert(0, notification);

      // Limit stored notifications
      if (_notifications.length > 100) {
        _notifications.removeRange(100, _notifications.length);
      }

      // Show local notification
      await _showLocalNotification(notification);

      // Notify listeners
      for (final listener in _listeners) {
        listener(notification);
      }

      print('Notification sent: ${notification.title}');
    } catch (e) {
      print('Failed to send notification: $e');
    }
  }

  /// Schedule notification for later
  Future<void> _scheduleNotification(
    AppNotification notification,
    DateTime scheduledTime,
  ) async {
    try {
      // In production, use flutter_local_notifications to schedule:
      // await flutterLocalNotificationsPlugin.zonedSchedule(
      //   notification.id,
      //   notification.title,
      //   notification.body,
      //   tz.TZDateTime.from(scheduledTime, tz.local),
      //   notificationDetails,
      //   uiLocalNotificationDateInterpretation: UILocalNotificationDateInterpretation.absoluteTime,
      // );

      print('Notification scheduled for: $scheduledTime');
    } catch (e) {
      print('Failed to schedule notification: $e');
    }
  }

  /// Show local notification
  Future<void> _showLocalNotification(AppNotification notification) async {
    try {
      // In production, show actual local notification:
      // const androidDetails = AndroidNotificationDetails(
      //   notification.channelId,
      //   'Channel Name',
      //   channelDescription: 'Channel Description',
      //   importance: Importance.high,
      //   priority: Priority.high,
      // );
      // const iosDetails = DarwinNotificationDetails();
      // const details = NotificationDetails(
      //   android: androidDetails,
      //   iOS: iosDetails,
      // );
      //
      // await flutterLocalNotificationsPlugin.show(
      //   notification.id,
      //   notification.title,
      //   notification.body,
      //   details,
      //   payload: jsonEncode(notification.data),
      // );

      print('Local notification shown: ${notification.title}');
    } catch (e) {
      print('Failed to show local notification: $e');
    }
  }

  /// Get status change title
  String _getStatusChangeTitle(LoanStatus status) {
    switch (status) {
      case LoanStatus.pending:
        //   return 'Application Received';
        // case LoanStatus.underReview:
        return 'Application Under Review';
      case LoanStatus.approved:
        return 'Application Approved! 🎉';
      case LoanStatus.rejected:
        return 'Application Update';
      case LoanStatus.disbursed:
        return 'Loan Disbursed! 💰';
    }
  }

  /// Get status change body
  String _getStatusChangeBody(
    String applicantName,
    LoanStatus oldStatus,
    LoanStatus newStatus,
  ) {
    switch (newStatus) {
      case LoanStatus.pending:
        return 'Your loan application has been received and is being processed.';
      // case LoanStatus.underReview:
      //   return 'Your application is now under review by our team.';
      case LoanStatus.approved:
        return 'Congratulations! Your loan application has been approved.';
      case LoanStatus.rejected:
        return 'Your loan application requires additional review. Please contact us for details.';
      case LoanStatus.disbursed:
        return 'Your loan has been successfully disbursed to your account.';
    }
  }

  /// Generate unique notification ID
  int _generateNotificationId() {
    return DateTime.now().millisecondsSinceEpoch % **********;
  }

  /// Get all notifications
  List<AppNotification> getNotifications({
    NotificationType? type,
    bool unreadOnly = false,
  }) {
    var filtered = _notifications.where((notification) {
      if (type != null && notification.type != type) return false;
      if (unreadOnly && notification.isRead) return false;
      return true;
    }).toList();

    return filtered;
  }

  /// Mark notification as read
  void markAsRead(int notificationId) {
    final index = _notifications.indexWhere((n) => n.id == notificationId);
    if (index != -1) {
      _notifications[index] = _notifications[index].copyWith(isRead: true);
    }
  }

  /// Mark all notifications as read
  void markAllAsRead() {
    for (int i = 0; i < _notifications.length; i++) {
      _notifications[i] = _notifications[i].copyWith(isRead: true);
    }
  }

  /// Delete notification
  void deleteNotification(int notificationId) {
    _notifications.removeWhere((n) => n.id == notificationId);
  }

  /// Clear all notifications
  void clearAllNotifications() {
    _notifications.clear();
  }

  /// Get unread count
  int get unreadCount => _notifications.where((n) => !n.isRead).length;

  /// Add notification listener
  void addListener(Function(AppNotification) listener) {
    _listeners.add(listener);
  }

  /// Remove notification listener
  void removeListener(Function(AppNotification) listener) {
    _listeners.remove(listener);
  }

  /// Get FCM token
  String? get fcmToken => _fcmToken;

  /// Update FCM token on server
  Future<void> updateTokenOnServer(String userId) async {
    if (_fcmToken == null) return;

    try {
      // In production, send token to your server:
      // await http.post(
      //   Uri.parse('$baseUrl/users/$userId/fcm-token'),
      //   headers: {'Content-Type': 'application/json'},
      //   body: jsonEncode({'token': _fcmToken}),
      // );

      print('FCM token updated on server for user: $userId');
    } catch (e) {
      print('Failed to update FCM token: $e');
    }
  }

  /// Setup periodic reminders
  Future<void> setupPeriodicReminders() async {
    // Daily reminder for pending applications
    await _schedulePeriodicReminder(
      'Daily Check',
      'You have pending loan applications to review',
      const Duration(days: 1),
    );

    // Weekly summary
    await _schedulePeriodicReminder(
      'Weekly Summary',
      'Check your loan application summary for this week',
      const Duration(days: 7),
    );
  }

  /// Schedule periodic reminder
  Future<void> _schedulePeriodicReminder(
    String title,
    String body,
    Duration interval,
  ) async {
    // In production, use a proper scheduling mechanism
    print(
        'Scheduled periodic reminder: $title (every ${interval.inDays} days)');
  }

  /// Cancel all scheduled notifications
  Future<void> cancelAllScheduledNotifications() async {
    try {
      // In production, cancel all scheduled notifications:
      // await flutterLocalNotificationsPlugin.cancelAll();

      print('All scheduled notifications cancelled');
    } catch (e) {
      print('Failed to cancel scheduled notifications: $e');
    }
  }

  /// Dispose resources
  void dispose() {
    _listeners.clear();
    _notifications.clear();
  }
}

/// Notification model
class AppNotification {
  final int id;
  final String title;
  final String body;
  final NotificationType type;
  final Map<String, dynamic> data;
  final DateTime timestamp;
  final String channelId;
  final bool isRead;
  final String? imageUrl;
  final String? actionUrl;

  const AppNotification({
    required this.id,
    required this.title,
    required this.body,
    required this.type,
    required this.data,
    required this.timestamp,
    required this.channelId,
    this.isRead = false,
    this.imageUrl,
    this.actionUrl,
  });

  AppNotification copyWith({
    int? id,
    String? title,
    String? body,
    NotificationType? type,
    Map<String, dynamic>? data,
    DateTime? timestamp,
    String? channelId,
    bool? isRead,
    String? imageUrl,
    String? actionUrl,
  }) {
    return AppNotification(
      id: id ?? this.id,
      title: title ?? this.title,
      body: body ?? this.body,
      type: type ?? this.type,
      data: data ?? this.data,
      timestamp: timestamp ?? this.timestamp,
      channelId: channelId ?? this.channelId,
      isRead: isRead ?? this.isRead,
      imageUrl: imageUrl ?? this.imageUrl,
      actionUrl: actionUrl ?? this.actionUrl,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'body': body,
      'type': type.name,
      'data': data,
      'timestamp': timestamp.toIso8601String(),
      'channel_id': channelId,
      'is_read': isRead,
      'image_url': imageUrl,
      'action_url': actionUrl,
    };
  }

  factory AppNotification.fromJson(Map<String, dynamic> json) {
    return AppNotification(
      id: json['id'],
      title: json['title'],
      body: json['body'],
      type: NotificationType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => NotificationType.system,
      ),
      data: Map<String, dynamic>.from(json['data']),
      timestamp: DateTime.parse(json['timestamp']),
      channelId: json['channel_id'],
      isRead: json['is_read'] ?? false,
      imageUrl: json['image_url'],
      actionUrl: json['action_url'],
    );
  }

  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
}

/// Notification types
enum NotificationType {
  applicationUpdate,
  reminder,
  system,
  marketing,
}

/// Notification channel
class NotificationChannel {
  final String id;
  final String name;
  final String description;
  final NotificationImportance importance;

  const NotificationChannel({
    required this.id,
    required this.name,
    required this.description,
    required this.importance,
  });
}

/// Notification importance levels
enum NotificationImportance {
  low,
  medium,
  high,
  urgent,
}

/// Custom exception for notification operations
class NotificationException implements Exception {
  final String message;
  final String? code;

  const NotificationException(this.message, [this.code]);

  @override
  String toString() => 'NotificationException: $message';
}
