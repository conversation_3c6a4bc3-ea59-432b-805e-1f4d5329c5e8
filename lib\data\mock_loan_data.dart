import '../models/loan_application.dart';

/// Mock data for Cambodian loan applications
/// This data is designed for UI showcase and workflow demonstration
class MockLoanData {
  static List<LoanApplication> getLoanApplications() {
    return [
      // Application 1 - Approved Tailor Shop
      LoanApplication(
        id: 'APP-001',
        status: LoanStatus.approved,
        applicationDate: DateTime(2024, 1, 15),
        requestedAmount: 5000000, // 5,000,000 KHR (~$1,250 USD)
        serviceFee: 250000, // 250,000 KHR
        loanTerm: 12, // 12 months
        decisionReason: 'ការអនុម័តដោយសារតែអាជីវកម្មស្ថិរភាព និងប្រវត្តិការសងប្រាក់ល្អ',
        groupName: 'ក្រុមកាត់ដេរស្រុកកំពង់ចាម',
        portfolioOfficer: PortfolioOfficer(
          id: 'PO-001',
          name: 'លោក សុខា វណ្ណារ៉ា',
          email: '<EMAIL>',
          phone: '+855 12 345 678',
          profileImageUrl: 'https://example.com/images/po_sokha.jpg',
        ),
        cpoApprover: CpoApprover(
          id: 'CPO-001',
          name: 'លោកស្រី ចន្ទ្រា ពេជ្រ',
          email: '<EMAIL>',
          approvalDate: DateTime(2024, 1, 20),
          approvalComments: 'អនុម័តសម្រាប់ការបង្កើនអាជីវកម្មកាត់ដេរ',
        ),
        applicant: Applicant(
          name: 'លោកស្រី ស្រីមុំ ចាន់ធី',
          address: 'ផ្ទះលេខ ១២៣ ផ្លូវ ២៧១ សង្កាត់បឹងកេងកង ខណ្ឌចំការមន រាជធានីភ្នំពេញ',
          nationalId: '010123456789',
          dateOfBirth: DateTime(1985, 3, 15),
          gender: 'ស្រី',
          maritalStatus: 'រៀបការ',
          education: 'បញ្ចប់វិទ្យាល័យ',
          occupation: 'ម្ចាស់ហាងកាត់ដេរ',
          phoneNumber: '+855 97 123 4567',
          nid: '010123456789',
          email: '<EMAIL>',
          profileImageUrl: 'https://example.com/images/sreimom.jpg',
          monthlyIncome: 1200000, // 1,200,000 KHR
        ),
        business: Business(
          name: 'ហាងកាត់ដេរស្រីមុំ',
          type: 'កាត់ដេរ',
          yearsInOperation: 5,
          monthlyIncome: 1500000, // 1,500,000 KHR
          address: 'ផ្សារចាស់ សង្កាត់ដូនពេញ ខណ្ឌដូនពេញ រាជធានីភ្នំពេញ',
          location: 'ភ្នំពេញ',
          description: 'ហាងកាត់ដេរសម្រាប់ស្ត្រី មានបទពិសោធន៍ជាង ៥ឆ្នាំ មានអតិថិជនទៀងទាត់',
        ),
        family: Family(
          spouseName: 'លោក ចាន់ធី សុវណ្ណ',
          spousePhone: '+855 96 987 6543',
          maritalStatus: 'រៀបការ',
          numberOfChildren: 2,
          dependents: 1,
          familyIncome: 2000000, // 2,000,000 KHR
          photoUrls: [
            'https://example.com/images/family1.jpg',
            'https://example.com/images/family2.jpg'
          ],
        ),
        assets: Assets(
          housePhotoUrl: 'https://example.com/images/house1.jpg',
          otherAssetsPhotoUrl: 'https://example.com/images/assets1.jpg',
          estimatedValue: 25000000, // 25,000,000 KHR
          ownershipType: 'owned',
          additionalAssets: [
            AssetItem(
              name: 'ម៉ាស៊ីនកាត់ដេរ',
              type: 'ឧបករណ៍ការងារ',
              value: 2000000,
              photoUrl: 'https://example.com/images/sewing_machine.jpg',
            ),
            AssetItem(
              name: 'ម៉ូតូ Honda',
              type: 'យានយន្ត',
              value: 3000000,
              photoUrl: 'https://example.com/images/motorbike.jpg',
            ),
          ],
        ),
        agreementUrl: 'https://example.com/agreements/APP-001.pdf',
        guarantor: Guarantor(
          id: 'G-001',
          name: 'លោក ពេជ្រ សុភាព',
          nid: '010987654321',
          phoneNumber: '+855 95 555 1234',
          address: 'ផ្ទះលេខ ៤៥៦ ផ្លូវ ៥៧ សង្កាត់បឹងកេងកង ខណ្ឌចំការមន រាជធានីភ្នំពេញ',
          relationship: 'បងប្រុស',
          occupation: 'គ្រូបង្រៀន',
          monthlyIncome: 800000,
          collateralPhotoUrl: 'https://example.com/images/collateral1.jpg',
          collateralValue: 15000000,
        ),
        groupMembers: [
          GroupMember(
            id: 'GM-001',
            name: 'លោកស្រី ស្រីពេជ្រ មុនីរ៉ា',
            phone: '+855 98 111 2222',
            email: '<EMAIL>',
            role: 'leader',
            joinDate: DateTime(2023, 6, 1),
          ),
          GroupMember(
            id: 'GM-002',
            name: 'លោកស្រី ចន្ទ្រា សុភា',
            phone: '+855 97 333 4444',
            role: 'member',
            joinDate: DateTime(2023, 6, 15),
          ),
          GroupMember(
            id: 'GM-003',
            name: 'លោកស្រី វណ្ណា ស្រីលក្ខ',
            phone: '+855 96 555 6666',
            role: 'secretary',
            joinDate: DateTime(2023, 7, 1),
          ),
        ],
        documents: {
          'national_id': 'https://example.com/docs/nid_001.jpg',
          'business_license': 'https://example.com/docs/license_001.jpg',
          'house_book': 'https://example.com/docs/house_book_001.jpg',
          'income_statement': 'https://example.com/docs/income_001.pdf',
        },
        createdAt: DateTime(2024, 1, 15),
        updatedAt: DateTime(2024, 1, 20),
      ),

      // Application 2 - Pending Rice Shop
      LoanApplication(
        id: 'APP-002',
        status: LoanStatus.pending,
        applicationDate: DateTime(2024, 2, 1),
        requestedAmount: 8000000, // 8,000,000 KHR
        serviceFee: 400000,
        loanTerm: 18,
        groupName: 'ក្រុមអ្នកលក់បាយស្រុកកំពត',
        portfolioOfficer: PortfolioOfficer(
          id: 'PO-002',
          name: 'លោក រតនា ស៊ីម',
          email: '<EMAIL>',
          phone: '+855 11 222 333',
          profileImageUrl: 'https://example.com/images/po_ratana.jpg',
        ),
        applicant: Applicant(
          name: 'លោក ធី សុវណ្ណ',
          address: 'ភូមិកំពង់ត្រាច សង្កាត់កំពង់ត្រាច ក្រុងកំពត ខេត្តកំពត',
          nationalId: '020234567890',
          dateOfBirth: DateTime(1978, 8, 22),
          gender: 'ប្រុស',
          maritalStatus: 'រៀបការ',
          education: 'បញ្ចប់បឋមសិក្សា',
          occupation: 'ម្ចាស់ហាងលក់បាយ',
          phoneNumber: '+855 98 765 4321',
          nid: '020234567890',
          email: '<EMAIL>',
          profileImageUrl: 'https://example.com/images/thy.jpg',
          monthlyIncome: 1800000,
        ),
        business: Business(
          name: 'ហាងបាយធី',
          type: 'ហាងអាហារ',
          yearsInOperation: 3,
          monthlyIncome: 2200000,
          address: 'ផ្សារកំពត សង្កាត់កំពង់ត្រាច ក្រុងកំពត ខេត្តកំពត',
          location: 'កំពត',
          description: 'ហាងលក់បាយ និងម្ហូបខ្មែរ មានកន្លែងអង្គុយ ២០កន្លែង',
        ),
        family: Family(
          spouseName: 'លោកស្រី សុវណ្ណ ចន្ទ្រា',
          spousePhone: '+855 97 888 9999',
          maritalStatus: 'រៀបការ',
          numberOfChildren: 3,
          dependents: 2,
          familyIncome: 2500000,
          photoUrls: [
            'https://example.com/images/family3.jpg',
            'https://example.com/images/family4.jpg'
          ],
        ),
        assets: Assets(
          housePhotoUrl: 'https://example.com/images/house2.jpg',
          otherAssetsPhotoUrl: 'https://example.com/images/assets2.jpg',
          estimatedValue: 18000000,
          ownershipType: 'owned',
          additionalAssets: [
            AssetItem(
              name: 'ឧបករណ៍ធ្វើម្ហូប',
              type: 'ឧបករណ៍ការងារ',
              value: 1500000,
            ),
            AssetItem(
              name: 'តុកៅអី',
              type: 'គ្រឿងសង្ហារឹម',
              value: 800000,
            ),
          ],
        ),
        agreementUrl: 'https://example.com/agreements/APP-002.pdf',
        guarantor: Guarantor(
          id: 'G-002',
          name: 'លោក ស៊ីម ពិសាច',
          nid: '020876543210',
          phoneNumber: '+855 94 777 8888',
          address: 'ភូមិព្រែកតាកុង សង្កាត់កំពង់ត្រាច ក្រុងកំពត ខេត្តកំពត',
          relationship: 'ម្ចាស់ដី',
          occupation: 'កសិករ',
          monthlyIncome: 600000,
          collateralPhotoUrl: 'https://example.com/images/collateral2.jpg',
          collateralValue: 12000000,
        ),
        groupMembers: [
          GroupMember(
            id: 'GM-004',
            name: 'លោក ពិសាច រតនា',
            phone: '+855 99 111 2222',
            role: 'leader',
            joinDate: DateTime(2023, 8, 1),
          ),
          GroupMember(
            id: 'GM-005',
            name: 'លោកស្រី សុភា វណ្ណា',
            phone: '+855 98 333 4444',
            role: 'member',
            joinDate: DateTime(2023, 8, 15),
          ),
        ],
        documents: {
          'national_id': 'https://example.com/docs/nid_002.jpg',
          'business_license': 'https://example.com/docs/license_002.jpg',
          'house_book': 'https://example.com/docs/house_book_002.jpg',
        },
        createdAt: DateTime(2024, 2, 1),
      ),

      // Application 3 - Disbursed Grocery Store
      LoanApplication(
        id: 'APP-003',
        status: LoanStatus.disbursed,
        applicationDate: DateTime(2023, 11, 10),
        requestedAmount: 3000000,
        serviceFee: 150000,
        loanTerm: 6,
        decisionReason: 'បានចេញប្រាក់ដោយជោគជ័យ',
        groupName: 'ក្រុមអ្នកលក់របស់ប្រើប្រាស់ស្រុកព្រៃវែង',
        portfolioOfficer: PortfolioOfficer(
          id: 'PO-003',
          name: 'លោកស្រី មុនីរ៉ា ចាន់',
          email: '<EMAIL>',
          phone: '+855 12 999 888',
        ),
        cpoApprover: CpoApprover(
          id: 'CPO-002',
          name: 'លោក វណ្ណារ៉ា ស៊ុន',
          email: '<EMAIL>',
          approvalDate: DateTime(2023, 11, 15),
          approvalComments: 'អនុម័តសម្រាប់ការបង្កើនទំនិញក្នុងហាង',
        ),
        applicant: Applicant(
          name: 'លោកស្រី ពេជ្រ សុខា',
          address: 'ភូមិព្រែកចាក សង្កាត់ព្រែកចាក ស្រុកព្រៃវែង ខេត្តព្រៃវែង',
          nationalId: '030345678901',
          dateOfBirth: DateTime(1990, 12, 5),
          gender: 'ស្រី',
          maritalStatus: 'នៅលីវ',
          education: 'បញ្ចប់វិទ្យាល័យ',
          occupation: 'ម្ចាស់ហាងទំនិញ',
          phoneNumber: '+855 96 444 5555',
          nid: '030345678901',
          email: '<EMAIL>',
          profileImageUrl: 'https://example.com/images/pich.jpg',
          monthlyIncome: 900000,
        ),
        business: Business(
          name: 'ហាងទំនិញពេជ្រ',
          type: 'ហាងទំនិញ',
          yearsInOperation: 2,
          monthlyIncome: 1100000,
          address: 'ផ្សារព្រៃវែង សង្កាត់ព្រៃវែង ស្រុកព្រៃវែង ខេត្តព្រៃវែង',
          location: 'ព្រៃវែង',
          description: 'ហាងលក់របស់ប្រើប្រាស់ប្រចាំថ្ងៃ សាប៊ូ ក្រែម និងអាហារស្ងួត',
        ),
        family: Family(
          maritalStatus: 'នៅលីវ',
          numberOfChildren: 0,
          dependents: 1,
          familyIncome: 1200000,
          photoUrls: ['https://example.com/images/family5.jpg'],
        ),
        assets: Assets(
          housePhotoUrl: 'https://example.com/images/house3.jpg',
          otherAssetsPhotoUrl: 'https://example.com/images/assets3.jpg',
          estimatedValue: 8000000,
          ownershipType: 'rented',
          additionalAssets: [
            AssetItem(
              name: 'ទូទឹកកក',
              type: 'ឧបករណ៍ការងារ',
              value: 1200000,
            ),
          ],
        ),
        agreementUrl: 'https://example.com/agreements/APP-003.pdf',
        guarantor: Guarantor(
          id: 'G-003',
          name: 'លោក ចាន់ ពិសាច',
          nid: '030765432109',
          phoneNumber: '+855 93 666 7777',
          address: 'ភូមិព្រែកតាមាស សង្កាត់ព្រៃវែង ស្រុកព្រៃវែង ខេត្តព្រៃវែង',
          relationship: 'ពូ',
          occupation: 'អ្នកធ្វើស្រែ',
          monthlyIncome: 500000,
          collateralPhotoUrl: 'https://example.com/images/collateral3.jpg',
          collateralValue: 8000000,
        ),
        groupMembers: [
          GroupMember(
            id: 'GM-006',
            name: 'លោកស្រី សុខា ម៉ាលី',
            phone: '+855 97 222 3333',
            role: 'leader',
            joinDate: DateTime(2023, 5, 1),
          ),
        ],
        documents: {
          'national_id': 'https://example.com/docs/nid_003.jpg',
          'business_license': 'https://example.com/docs/license_003.jpg',
          'rental_agreement': 'https://example.com/docs/rental_003.pdf',
        },
        createdAt: DateTime(2023, 11, 10),
        updatedAt: DateTime(2023, 11, 20),
      ),

      // Application 4 - Rejected Fish Selling
      LoanApplication(
        id: 'APP-004',
        status: LoanStatus.rejected,
        applicationDate: DateTime(2024, 1, 25),
        requestedAmount: 12000000,
        serviceFee: 600000,
        loanTerm: 24,
        decisionReason: 'ចំណូលមិនគ្រប់គ្រាន់សម្រាប់ការសងប្រាក់',
        groupName: 'ក្រុមអ្នកលក់ត្រីស្រុកកំពង់ស្ពឺ',
        portfolioOfficer: PortfolioOfficer(
          id: 'PO-001',
          name: 'លោក សុខា វណ្ណារ៉ា',
          email: '<EMAIL>',
          phone: '+855 12 345 678',
        ),
        cpoApprover: CpoApprover(
          id: 'CPO-001',
          name: 'លោកស្រី ចន្ទ្រា ពេជ្រ',
          email: '<EMAIL>',
          approvalDate: DateTime(2024, 2, 5),
          approvalComments: 'បដិសេធដោយសារចំណូលមិនស្ថិរភាព',
        ),
        applicant: Applicant(
          name: 'លោក សុវណ្ណ រតនា',
          address: 'ភូមិកំពង់ព្រែក សង្កាត់កំពង់ព្រែក ស្រុកកំពង់ស្ពឺ ខេត្តកំពង់ស្ពឺ',
          nationalId: '040456789012',
          dateOfBirth: DateTime(1982, 4, 18),
          gender: 'ប្រុស',
          maritalStatus: 'រៀបការ',
          education: 'មិនបានចូលរៀន',
          occupation: 'អ្នកលក់ត្រី',
          phoneNumber: '+855 95 123 4567',
          nid: '040456789012',
          email: '<EMAIL>',
          profileImageUrl: 'https://example.com/images/sovann.jpg',
          monthlyIncome: 800000,
        ),
        business: Business(
          name: 'ការលក់ត្រីសុវណ្ណ',
          type: 'លក់ត្រី',
          yearsInOperation: 1,
          monthlyIncome: 900000,
          address: 'ផ្សារត្រីកំពង់ស្ពឺ សង្កាត់កំពង់ស្ពឺ ស្រុកកំពង់ស្ពឺ ខេត្តកំពង់ស្ពឺ',
          location: 'កំពង់ស្ពឺ',
          description: 'លក់ត្រីស្រស់ពីសមុទ្រ ប៉ុន្តែចំណូលមិនទៀងទាត់',
        ),
        family: Family(
          spouseName: 'លោកស្រី រតនា ស្រីពេជ្រ',
          spousePhone: '+855 94 987 6543',
          maritalStatus: 'រៀបការ',
          numberOfChildren: 4,
          dependents: 3,
          familyIncome: 1000000,
          photoUrls: [
            'https://example.com/images/family6.jpg',
            'https://example.com/images/family7.jpg'
          ],
        ),
        assets: Assets(
          housePhotoUrl: 'https://example.com/images/house4.jpg',
          otherAssetsPhotoUrl: 'https://example.com/images/assets4.jpg',
          estimatedValue: 5000000,
          ownershipType: 'family',
          additionalAssets: [
            AssetItem(
              name: 'ទូទឹកកកតូច',
              type: 'ឧបករណ៍ការងារ',
              value: 800000,
            ),
          ],
        ),
        agreementUrl: 'https://example.com/agreements/APP-004.pdf',
        guarantor: Guarantor(
          id: 'G-004',
          name: 'លោក ពេជ្រ មុនី',
          nid: '040654321098',
          phoneNumber: '+855 92 555 6666',
          address: 'ភូមិព្រែកតាសុខ សង្កាត់កំពង់ស្ពឺ ស្រុកកំពង់ស្ពឺ ខេត្តកំពង់ស្ពឺ',
          relationship: 'បងប្រុស',
          occupation: 'នេសាទ',
          monthlyIncome: 400000,
          collateralPhotoUrl: 'https://example.com/images/collateral4.jpg',
          collateralValue: 3000000,
        ),
        groupMembers: [
          GroupMember(
            id: 'GM-007',
            name: 'លោក មុនី ស៊ុន',
            phone: '+855 96 777 8888',
            role: 'leader',
            joinDate: DateTime(2023, 12, 1),
          ),
          GroupMember(
            id: 'GM-008',
            name: 'លោក ស៊ុន វណ្ណា',
            phone: '+855 95 999 0000',
            role: 'member',
            joinDate: DateTime(2023, 12, 15),
          ),
        ],
        documents: {
          'national_id': 'https://example.com/docs/nid_004.jpg',
          'house_book': 'https://example.com/docs/house_book_004.jpg',
        },
        createdAt: DateTime(2024, 1, 25),
        updatedAt: DateTime(2024, 2, 5),
      ),
    ];
  }

  /// Get applications by status
  static List<LoanApplication> getApplicationsByStatus(LoanStatus status) {
    return getLoanApplications().where((app) => app.status == status).toList();
  }

  /// Get applications for specific portfolio officer
  static List<LoanApplication> getApplicationsForPO(String poId) {
    return getLoanApplications()
        .where((app) => app.portfolioOfficer.id == poId)
        .toList();
  }

  /// Get dashboard statistics
  static Map<String, dynamic> getDashboardStats() {
    final applications = getLoanApplications();
    final totalApplications = applications.length;
    final pendingCount = applications.where((app) => app.status == LoanStatus.pending).length;
    final approvedCount = applications.where((app) => app.status == LoanStatus.approved).length;
    final disbursedCount = applications.where((app) => app.status == LoanStatus.disbursed).length;
    final rejectedCount = applications.where((app) => app.status == LoanStatus.rejected).length;
    
    final totalRequestedAmount = applications.fold<double>(
      0, (sum, app) => sum + app.requestedAmount
    );
    
    final totalDisbursedAmount = applications
        .where((app) => app.status == LoanStatus.disbursed)
        .fold<double>(0, (sum, app) => sum + app.requestedAmount);

    return {
      'totalApplications': totalApplications,
      'pendingCount': pendingCount,
      'approvedCount': approvedCount,
      'disbursedCount': disbursedCount,
      'rejectedCount': rejectedCount,
      'totalRequestedAmount': totalRequestedAmount,
      'totalDisbursedAmount': totalDisbursedAmount,
      'averageLoanAmount': totalApplications > 0 ? totalRequestedAmount / totalApplications : 0,
      'approvalRate': totalApplications > 0 ? (approvedCount + disbursedCount) / totalApplications * 100 : 0,
    };
  }

  /// Get portfolio officers list
  static List<PortfolioOfficer> getPortfolioOfficers() {
    return [
      PortfolioOfficer(
        id: 'PO-001',
        name: 'លោក សុខា វណ្ណារ៉ា',
        email: '<EMAIL>',
        phone: '+855 12 345 678',
        profileImageUrl: 'https://example.com/images/po_sokha.jpg',
      ),
      PortfolioOfficer(
        id: 'PO-002',
        name: 'លោក រតនា ស៊ីម',
        email: '<EMAIL>',
        phone: '+855 11 222 333',
        profileImageUrl: 'https://example.com/images/po_ratana.jpg',
      ),
      PortfolioOfficer(
        id: 'PO-003',
        name: 'លោកស្រី មុនីរ៉ា ចាន់',
        email: '<EMAIL>',
        phone: '+855 12 999 888',
        profileImageUrl: 'https://example.com/images/po_munira.jpg',
      ),
    ];
  }

  /// Get sample business types for Cambodia
  static List<String> getBusinessTypes() {
    return [
      'កាត់ដេរ',
      'ហាងអាហារ',
      'ហាងទំនិញ',
      'លក់ត្រី',
      'លក់បន្លែ',
      'ហាងកាហ្វេ',
      'ហាងកាត់សក់',
      'ជួសជុលម៉ូតូ',
      'លក់សម្លៀកបំពាក់',
      'ហាងថ្នាំ',
      'ហាងលក់ទូរស័ព្ទ',
      'ហាងលក់គ្រឿងសង្ហារឹម',
      'កសិកម្ម',
      'ចិញ្ចឹមសត្វ',
      'ផលិតកម្ម',
    ];
  }

  /// Get sample Cambodian provinces
  static List<String> getProvinces() {
    return [
      'រាជធានីភ្នំពេញ',
      'កំពត',
      'ព្រៃវែង',
      'កំពង់ស្ពឺ',
      'កំពង់ចាម',
      'កំពង់ធំ',
      'កណ្តាល',
      'តាកែវ',
      'ស្វាយរៀង',
      'បាត់ដំបង',
      'បន្ទាយមានជ័យ',
      'សៀមរាប',
      'ព្រះវិហារ',
      'ស្ទឹងត្រែង',
      'រតនគិរី',
      'មណ្ឌលគិរី',
      'ក្រចេះ',
      'ពោធិ៍សាត់',
      'ឧត្តរមានជ័យ',
      'កែប',
      'ប៉ៃលិន',
      'ត្បូងឃ្មុំ',
      'កោះកុង',
      'ព្រះសីហនុ',
    ];
  }
}