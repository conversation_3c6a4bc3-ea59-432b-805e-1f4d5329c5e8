# LC Mobile - Flutter Development Guide

This document provides a comprehensive overview of the LC Mobile prototype, serving as a blueprint for its implementation in Flutter. It covers user workflows, business logic, data structures, and UI/UX design principles.

## 1. Project Overview

LC Mobile is a mobile-first application for a microfinance institution designed to streamline the loan application, review, and disbursement process. It caters to three primary user roles, each with a dedicated interface and set of responsibilities.

### Key Features:

- Role-based dashboards for different user types.
- A multi-step, mobile-friendly loan application form.
- AI-powered National ID scanning to pre-fill applicant data (OCR).
- AI-powered risk analysis to assist with loan approval decisions.
- Support for both individual and group loans.
- Direct camera integration for photo uploads.

---

## 2. User Roles & Workflows

The application is built around three distinct user roles:

### A. Portfolio Officer (PO)

The PO is the field agent responsible for client interaction and creating new loan applications.

**PO Workflow:**

1.  **Dashboard View:** The PO logs in and sees their main dashboard.
    - **Analytics:** Displays key stats: Total Applications, Total Loan Value, Approved Loans, and Approval Rate.
    - **Status Chart:** A bar chart visualizing the status of all their applications.
    - **Application List:** A grid of cards, each representing a loan application they have submitted.
2.  **Create New Application:** The PO clicks a "New Application" button to start the process.
3.  **Multi-Step Form:** The PO fills out a 7-step form, capturing all necessary client information.
    - **Step 1: AI-Powered NID Scan:** The PO can use the device's camera to scan the client's National ID card. An AI service extracts the name and date of birth, pre-filling the form fields.
    - **Step 2: Group Loan Details:** The PO can specify if it's a group loan, and if so, add the names and phone numbers of group members.
    - **Steps 3-7: Data Capture:** The PO enters business, family, and guarantor details.
    - **Photo Uploads:** The PO uses the device camera to capture photos of the applicant, their house, assets, and signed agreement.
4.  **Submission:** After completing all steps, the PO submits the form. The application status is set to `Pending`.

### B. Credit Pipe Officer (CPO)

The CPO is a back-office role responsible for reviewing and making decisions on loan applications.

**CPO Workflow:**

1.  **Dashboard View:** The CPO logs in and sees a dashboard containing only applications with a `Pending` status.
2.  **Review Application:** The CPO selects an application card to view its full details.
3.  **Detail Page:** This page displays all information captured by the PO. A key component here is the **AI-Powered Risk Analysis** card.
    - **AI Analysis:** An AI service analyzes the application data (loan amount, business type, family size, etc.) and provides:
      - A brief summary.
      - A recommended risk level (`Low`, `Medium`, `High`).
      - A list of positive factors.
      - A list of potential risks or negative factors.
4.  **Decision Making:** Based on the application data and AI analysis, the CPO can:
    - **Approve:** Change the application status to `Approved`.
    - **Reject:** Change the status to `Rejected` and must provide a reason for the decision.

### C. Teller

The Teller is responsible for disbursing the funds for approved loans.

**Teller Workflow:**

1.  **Dashboard View:** The Teller logs in and sees a dashboard containing only applications with an `Approved` status.
2.  **Verify & Disburse:** The Teller selects an application to review the details and confirm the identity of the recipient. The card prominently displays the name of the CPO who approved the loan.
3.  **Confirm Disbursement:** The Teller clicks a "Confirm Disbursement" button, which changes the application status to `Disbursed`.

---

## 3. Data Models & Dummy Data

The core of the application revolves around the `LoanApplication` object.

### `LoanApplication`

```json
{
  "id": "app-001",
  "status": "Approved", // "Pending" | "Approved" | "Rejected" | "Disbursed"
  "applicationDate": "2024-07-15",
  "requestedAmount": 5000000,
  "serviceFee": 100000,
  "loanTerm": 24, // in months
  "decisionReason": "Strong business plan and good collateral.",
  "groupName": "Optional Group Name",
  "portfolioOfficer": {
    "name": "Sokha Chen"
  },
  "cpoApprover": {
    "name": "Chhaya Lim"
  },
  "applicant": {
    "name": "Sokha Chen",
    "dob": "1985-04-12",
    "phone": "012-345-678",
    "photoUrl": "https://placehold.co/100x100.png"
  },
  "business": {
    "location": "Phnom Penh",
    "type": "Street Food Vendor"
  },
  "family": {
    "spouseName": "Bopha Chen",
    "childrenCount": 2
  },
  "assets": {
    "housePhotoUrl": "https://placehold.co/400x300.png",
    "otherAssetsPhotoUrl": "https://placehold.co/400x300.png"
  },
  "agreementUrl": "https://placehold.co/400x500.png",
  "guarantor": {
    "id": "guar-001",
    "name": "Vannak Ouk",
    "phone": "098-765-432",
    "collateralPhotoUrl": "https://placehold.co/400x300.png"
  },
  "groupMembers": [
    { "name": "Srey Leak", "phone": "012-111-222" },
    { "name": "Chanthou Oum", "phone": "012-333-444" }
  ]
}
```

---

## 4. UI Design & Components

The UI should be clean, modern, and mobile-first, with a professional and trustworthy feel.

### Color Palette & Theme

- **Primary:** A calming teal/green (`hsl(171, 38%, 45%)`). Used for primary buttons and active states.
- **Background:** A very light, slightly green-tinted white (`hsl(170, 53%, 92%)`).
- **Cards:** An off-white, almost pure white with a hint of the background color (`hsl(170, 53%, 98%)`).
- **Text:** A dark, muted teal for good readability (`hsl(171, 25%, 25%)`).
- **Accent Gradients:**
  - **Gold/Orange:** Used for primary call-to-action buttons like "Submit Application" (`khmer-gold-gradient`).
  - **Blue:** Used for the AI Analysis card background to make it stand out (`temple-blue-gradient`).
  - **Pink/Fuchsia:** Used for the "Disburse" button (`lotus-pink-gradient`).

### Key Screens & Components

#### A. Main Layout

- A persistent header with the app title and a user profile dropdown menu.
- The main content area uses padding for spacing.

#### B. Dashboards (PO, CPO, Teller)

- **Layout:** A responsive grid of `LoanApplicationCard` components.
- **PO Dashboard Extras:**
  - `StatCard`: Row of 4 cards at the top for key metrics. Each has a title, large value, and an icon.
  - `ApplicationStatusChart`: A bar chart component showing counts for each loan status.
- **`LoanApplicationCard` Component:**
  - **Structure:** `Card` with `CardHeader`, `CardContent`, `CardFooter`.
  - **Header:** Contains the applicant's `Avatar`, `name` (CardTitle), and `business type` (CardDescription). A `Badge` in the top-right corner shows the `status`.
  - **Content:** A list of key details, each with an `Icon`, `Label`, and `Value`. (e.g., Banknote icon for amount, Calendar icon for date, User icon for PO name).
  - **Footer:** A "View Details" link with an arrow icon.

#### C. Application Detail Page

- **Layout:** A single-column layout of various cards, each displaying a logical section of the application.
- **Main Header Card:** Shows the applicant's photo, name, application ID, and a large status badge. Also includes key financial details like amount, fee, and term.
- **Section Cards:**
  - Applicant Details (DOB, Phone)
  - Business Details (Type, Location)
  - Family Details (Spouse, Children)
  - Group Loan Details (if applicable)
  - **AI-Powered Risk Analysis:** A visually distinct card (using the blue gradient) that shows the AI's output. It's broken down into Summary, Risk Level, Positive Factors, and Potential Risks, each with a relevant icon.
  - Uploaded Documents & Assets: A grid of images for the house, assets, and agreement.
  - Guarantor Information.
- **Actions Card:** A final card at the bottom with "Approve" and "Reject" buttons for the CPO.

#### D. New Application Form (`LoanApplicationForm`)

- **Structure:** A single large `Card` that contains a multi-step wizard.
- **Progress:** A `Progress` bar and "Step X of Y" title at the top indicate progress.
- **Navigation:** "Next" and "Previous" buttons to move between steps. The final step has a "Submit Application" button.
- **Input Fields:** Standard text, number, and date inputs with clear labels. Validation messages appear below fields if there are errors.
- **Special Components:**
  - **`CameraCapture`:** Used for the applicant's photo and NID scan. It shows a placeholder and, on click, opens a dialog with a live camera feed to take a photo.
  - **`FileUpload`:** A component that allows either capturing a photo or selecting a file from the device's storage.
  - **Group Members:** A dynamic list where the PO can add or remove member entries (name/phone).

---

## 5. AI Integration (Backend Logic)

For Flutter, you will need to call backend API endpoints that wrap the following Genkit flows.

### A. `extractNidInfo`

- **Purpose:** To perform OCR on a Cambodian National ID card image.
- **Input:** A JSON object with a single key:
  - `photoDataUri`: A Base64 encoded string of the ID card image, including the data URI prefix (e.g., `data:image/jpeg;base64,...`).
- **Output:** A JSON object with extracted details:
  ```json
  {
    "applicantName": "Sokha Chen",
    "applicantDob": "1985-04-12"
  }
  ```

### B. `analyzeLoanApplication`

- **Purpose:** To analyze application data and provide a risk assessment.
- **Input:** A JSON object matching the loan application's structure.
  ```json
  {
    "applicantName": "Nary Kim",
    "requestedAmount": 3500000,
    "businessType": "Tailor Shop",
    "family": {
      "spouseName": "Piseth Kim",
      "childrenCount": 1
    },
    "loanTerm": 18,
    "isGroupLoan": true,
    "groupMembersCount": 2
  }
  ```
- **Output:** A JSON object with the AI's analysis:
  ```json
  {
    "summary": "A brief, one-paragraph summary of the loan application.",
    "riskLevel": "Medium", // "Low" | "Medium" | "High"
    "positiveFactors": ["Factor 1", "Factor 2"],
    "negativeFactors": ["Risk 1", "Risk 2"]
  }
  ```
