import 'package:flutter/material.dart';
import 'package:le_mobile_app/utils/colors.dart';

class ApplicationStatusChart extends StatelessWidget {
  final Map<String, int> data;

  const ApplicationStatusChart({
    super.key,
    required this.data,
  });

  @override
  Widget build(BuildContext context) {
    final pendingCount = data['Pending'] ?? 0;
    final approvedCount = data['Approved'] ?? 0;
    final rejectedCount = data['Rejected'] ?? 0;
    final disbursedCount = data['Disbursed'] ?? 0;
    final total = pendingCount + approvedCount + rejectedCount + disbursedCount;
    
    if (total == 0) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Text('No applications yet'),
        ),
      );
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Application Status',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildBar('Pending', pendingCount, AppColors.pending, total),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildBar('Approved', approvedCount, AppColors.approved, total),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildBar('Rejected', rejectedCount, AppColors.rejected, total),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildBar('Disbursed', disbursedCount, AppColors.disbursed, total),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBar(String label, int count, Color color, int total) {
    final height = total > 0 ? (count / total * 100).clamp(5.0, 100.0) : 5.0;
    
    return Column(
      children: [
        Container(
          height: height,
          width: double.infinity,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(4),
          ),
        ),
        const SizedBox(height: 8),
        Text(
          count.toString(),
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: AppColors.textPrimary,
          ),
        ),
      ],
    );
  }
}