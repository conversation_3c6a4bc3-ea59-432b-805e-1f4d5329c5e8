import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/loan_application.dart';
import 'base_loan_service.dart';

/// Service class for handling loan application API calls and business logic
class LoanApplicationService extends BaseLoanService {
  static const String _baseUrl = 'https://api.loancompany.com/v1';
  static const Duration _timeout = Duration(seconds: 30);

  /// Singleton pattern for service instance
  static final LoanApplicationService _instance = LoanApplicationService._internal();
  factory LoanApplicationService() => _instance;
  LoanApplicationService._internal();

  /// HTTP client with timeout configuration
  final http.Client _client = http.Client();

  /// Common headers for API requests
  Map<String, String> get _headers => {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    // TODO: Add authentication headers when available
    // 'Authorization': 'Bearer ${AuthService.token}',
  };

  /// Fetch all loan applications with optional filtering
  @override
  Future<List<LoanApplication>> fetchApplications({
    LoanStatus? status,
    String? portfolioOfficerId,
    DateTime? fromDate,
    DateTime? toDate,
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final queryParams = <String, String>{
        'page': page.toString(),
        'limit': limit.toString(),
      };

      if (status != null) queryParams['status'] = status.name;
      if (portfolioOfficerId != null) queryParams['po_id'] = portfolioOfficerId;
      if (fromDate != null) queryParams['from_date'] = fromDate.toIso8601String();
      if (toDate != null) queryParams['to_date'] = toDate.toIso8601String();

      final uri = Uri.parse('$_baseUrl/applications').replace(
        queryParameters: queryParams,
      );

      final response = await _client
          .get(uri, headers: _headers)
          .timeout(_timeout);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final applications = (data['applications'] as List)
            .map((json) => LoanApplication.fromJson(json))
            .toList();
        return applications;
      } else {
        throw LoanApplicationException(
          'Failed to fetch applications: ${response.statusCode}',
          response.statusCode,
        );
      }
    } catch (e) {
      if (e is LoanApplicationException) rethrow;
      throw LoanApplicationException('Network error: $e');
    }
  }

  /// Fetch a single loan application by ID
  @override
  Future<LoanApplication> fetchApplicationById(String id) async {
    try {
      final response = await _client
          .get(
            Uri.parse('$_baseUrl/applications/$id'),
            headers: _headers,
          )
          .timeout(_timeout);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return LoanApplication.fromJson(data);
      } else if (response.statusCode == 404) {
        throw LoanApplicationException('Application not found', 404);
      } else {
        throw LoanApplicationException(
          'Failed to fetch application: ${response.statusCode}',
          response.statusCode,
        );
      }
    } catch (e) {
      if (e is LoanApplicationException) rethrow;
      throw LoanApplicationException('Network error: $e');
    }
  }

  /// Create a new loan application
  @override
  Future<LoanApplication> createApplication(LoanApplication application) async {
    try {
      final response = await _client
          .post(
            Uri.parse('$_baseUrl/applications'),
            headers: _headers,
            body: json.encode(application.toJson()),
          )
          .timeout(_timeout);

      if (response.statusCode == 201) {
        final data = json.decode(response.body);
        return LoanApplication.fromJson(data);
      } else {
        final errorData = json.decode(response.body);
        throw LoanApplicationException(
          errorData['message'] ?? 'Failed to create application',
          response.statusCode,
        );
      }
    } catch (e) {
      if (e is LoanApplicationException) rethrow;
      throw LoanApplicationException('Network error: $e');
    }
  }

  /// Update an existing loan application
  @override
  Future<LoanApplication> updateApplication(LoanApplication application) async {
    try {
      final response = await _client
          .put(
            Uri.parse('$_baseUrl/applications/${application.id}'),
            headers: _headers,
            body: json.encode(application.toJson()),
          )
          .timeout(_timeout);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return LoanApplication.fromJson(data);
      } else {
        final errorData = json.decode(response.body);
        throw LoanApplicationException(
          errorData['message'] ?? 'Failed to update application',
          response.statusCode,
        );
      }
    } catch (e) {
      if (e is LoanApplicationException) rethrow;
      throw LoanApplicationException('Network error: $e');
    }
  }

  /// Approve a loan application
  @override
  Future<LoanApplication> approveApplication(
    String applicationId,
    String approverId,
    String? comments,
  ) async {
    try {
      final requestBody = {
        'status': LoanStatus.approved.name,
        'approver_id': approverId,
        'approval_comments': comments,
        'approval_date': DateTime.now().toIso8601String(),
      };

      final response = await _client
          .patch(
            Uri.parse('$_baseUrl/applications/$applicationId/approve'),
            headers: _headers,
            body: json.encode(requestBody),
          )
          .timeout(_timeout);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return LoanApplication.fromJson(data);
      } else {
        final errorData = json.decode(response.body);
        throw LoanApplicationException(
          errorData['message'] ?? 'Failed to approve application',
          response.statusCode,
        );
      }
    } catch (e) {
      if (e is LoanApplicationException) rethrow;
      throw LoanApplicationException('Network error: $e');
    }
  }

  /// Reject a loan application
  @override
  Future<LoanApplication> rejectApplication(
    String applicationId,
    String reason,
  ) async {
    try {
      final requestBody = {
        'status': LoanStatus.rejected.name,
        'decision_reason': reason,
        'decision_date': DateTime.now().toIso8601String(),
      };

      final response = await _client
          .patch(
            Uri.parse('$_baseUrl/applications/$applicationId/reject'),
            headers: _headers,
            body: json.encode(requestBody),
          )
          .timeout(_timeout);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return LoanApplication.fromJson(data);
      } else {
        final errorData = json.decode(response.body);
        throw LoanApplicationException(
          errorData['message'] ?? 'Failed to reject application',
          response.statusCode,
        );
      }
    } catch (e) {
      if (e is LoanApplicationException) rethrow;
      throw LoanApplicationException('Network error: $e');
    }
  }

  /// Disburse a loan
  @override
  Future<LoanApplication> disburseLoan(
    String applicationId,
    double amount,
    String disbursementMethod,
  ) async {
    try {
      final requestBody = {
        'status': LoanStatus.disbursed.name,
        'disbursed_amount': amount,
        'disbursement_method': disbursementMethod,
        'disbursement_date': DateTime.now().toIso8601String(),
      };

      final response = await _client
          .patch(
            Uri.parse('$_baseUrl/applications/$applicationId/disburse'),
            headers: _headers,
            body: json.encode(requestBody),
          )
          .timeout(_timeout);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return LoanApplication.fromJson(data);
      } else {
        final errorData = json.decode(response.body);
        throw LoanApplicationException(
          errorData['message'] ?? 'Failed to disburse application',
          response.statusCode,
        );
      }
    } catch (e) {
      if (e is LoanApplicationException) rethrow;
      throw LoanApplicationException('Network error: $e');
    }
  }

  /// Get application statistics for dashboard
  @override
  Future<ApplicationStatistics> getApplicationStatistics() async {
    return getApplicationStatisticsWithFilters();
  }

  /// Get application statistics with optional filters
  Future<ApplicationStatistics> getApplicationStatisticsWithFilters({
    String? portfolioOfficerId,
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    try {
      final queryParams = <String, String>{};
      if (portfolioOfficerId != null) queryParams['po_id'] = portfolioOfficerId;
      if (fromDate != null) queryParams['from_date'] = fromDate.toIso8601String();
      if (toDate != null) queryParams['to_date'] = toDate.toIso8601String();

      final uri = Uri.parse('$_baseUrl/applications/statistics').replace(
        queryParameters: queryParams,
      );

      final response = await _client
          .get(uri, headers: _headers)
          .timeout(_timeout);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return ApplicationStatistics.fromJson(data);
      } else {
        throw LoanApplicationException(
          'Failed to fetch statistics: ${response.statusCode}',
          response.statusCode,
        );
      }
    } catch (e) {
      if (e is LoanApplicationException) rethrow;
      throw LoanApplicationException('Network error: $e');
    }
  }

  /// Search applications by query
  @override
  Future<List<LoanApplication>> searchApplications(String query) async {
    try {
      final response = await _client
          .get(
            Uri.parse('$_baseUrl/applications/search?q=${Uri.encodeComponent(query)}'),
            headers: _headers,
          )
          .timeout(_timeout);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final applications = (data['applications'] as List)
            .map((json) => LoanApplication.fromJson(json))
            .toList();
        return applications;
      } else {
        throw LoanApplicationException(
          'Failed to search applications: ${response.statusCode}',
          response.statusCode,
        );
      }
    } catch (e) {
      if (e is LoanApplicationException) rethrow;
      throw LoanApplicationException('Network error: $e');
    }
  }

  /// Get portfolio officers
  @override
  Future<List<PortfolioOfficer>> getPortfolioOfficers() async {
    try {
      final response = await _client
          .get(
            Uri.parse('$_baseUrl/portfolio-officers'),
            headers: _headers,
          )
          .timeout(_timeout);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final officers = (data['officers'] as List)
            .map((json) => PortfolioOfficer.fromJson(json))
            .toList();
        return officers;
      } else {
        throw LoanApplicationException(
          'Failed to fetch portfolio officers: ${response.statusCode}',
          response.statusCode,
        );
      }
    } catch (e) {
      if (e is LoanApplicationException) rethrow;
      throw LoanApplicationException('Network error: $e');
    }
  }

  /// Upload document
  @override
  Future<String> uploadDocument(
    String applicationId,
    String documentType,
    String filePath,
  ) async {
    try {
      final request = http.MultipartRequest(
        'POST',
        Uri.parse('$_baseUrl/applications/$applicationId/documents'),
      );

      request.headers.addAll(_headers);
      request.fields['document_type'] = documentType;
      request.files.add(await http.MultipartFile.fromPath('document', filePath));

      final streamedResponse = await request.send().timeout(_timeout);
      final response = await http.Response.fromStream(streamedResponse);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data['document_url'];
      } else {
        throw LoanApplicationException(
          'Failed to upload document: ${response.statusCode}',
          response.statusCode,
        );
      }
    } catch (e) {
      if (e is LoanApplicationException) rethrow;
      throw LoanApplicationException('Document upload error: $e');
    }
  }

  /// Upload file (photo, document)
  Future<String> uploadFile(String filePath, String fileType) async {
    try {
      final request = http.MultipartRequest(
        'POST',
        Uri.parse('$_baseUrl/upload'),
      );

      request.headers.addAll(_headers);
      request.fields['file_type'] = fileType;
      request.files.add(await http.MultipartFile.fromPath('file', filePath));

      final streamedResponse = await request.send().timeout(_timeout);
      final response = await http.Response.fromStream(streamedResponse);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data['file_url'];
      } else {
        throw LoanApplicationException(
          'Failed to upload file: ${response.statusCode}',
          response.statusCode,
        );
      }
    } catch (e) {
      if (e is LoanApplicationException) rethrow;
      throw LoanApplicationException('File upload error: $e');
    }
  }

  /// Dispose resources
  void dispose() {
    _client.close();
  }
}

/// Custom exception for loan application operations
class LoanApplicationException implements Exception {
  final String message;
  final int? statusCode;

  LoanApplicationException(this.message, [this.statusCode]);

  @override
  String toString() => 'LoanApplicationException: $message';
}